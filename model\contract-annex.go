package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// ContractAnnex ...
type ContractAnnex struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	Code            string             `json:"code,omitempty" bson:"code,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64  `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64  `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64  `json:"entityID,omitempty" bson:"entity_id,omitempty"`

	ContractID   int64  `json:"contractID,omitempty" bson:"contract_id,omitempty"`
	ContractCode string `json:"contractCode,omitempty" bson:"contract_code,omitempty"`
	BidID        *int64 `json:"bidID,omitempty" bson:"bid_id,omitempty"`

	AnnexID      int64      `json:"annexID,omitempty" bson:"annex_id,omitempty"`
	AnnexNumber  string     `json:"annexNumber,omitempty" bson:"annex_number,omitempty"`
	Name         string     `json:"name,omitempty" bson:"name,omitempty"`
	ContractDate *time.Time `json:"contractDate,omitempty" bson:"contract_date,omitempty"`
	ExpireDate   *time.Time `json:"expireDate,omitempty" bson:"expire_date,omitempty"`
	Content      string     `json:"content,omitempty" bson:"content,omitempty"`
	Attachments  []string   `json:"attachments,omitempty" bson:"attachments,omitempty"`

	HashTag string `json:"-" bson:"hash_tag,omitempty"` // for search

	// ForQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// ContractAnnexDB ...
var ContractAnnexDB = &mongodb.Instance[*ContractAnnex]{
	ColName: "contract_annexes",
}

// InitContractAnnexModel ....
func InitContractAnnexModel(s *mongo.Database) {
	ContractAnnexDB.ApplyDatabase(s)

	t := true
	ContractAnnexDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	ContractAnnexDB.CreateIndex(bson.D{
		{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	ContractAnnexDB.CreateIndex(bson.D{
		{Key: "annex_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})
}
