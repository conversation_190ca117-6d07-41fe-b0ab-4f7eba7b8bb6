package product

type SkuMainRequest struct {
	Code        string `json:"code,omitempty" bson:"code,omitempty"` // sku code
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SellerCode  string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Slug        string `json:"slug,omitempty" bson:"slug,omitempty"`
	Type        string `json:"type,omitempty" bson:"type,omitempty"`     // normal/deal/combo
	Status      string `json:"status,omitempty" bson:"status,omitempty"` //default
}

type SkuItemRequest struct {
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"` // sku code
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SellerCode  string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerClass string `json:"sellerClass,omitempty"` // INTERNAL
	Status      string `json:"status"`

	RetailPriceType  string   `json:"retailPriceType,omitempty" bson:"retail_price_type,omitempty"` // FIXED_PRICE
	RetailPriceValue int64    `json:"retailPriceValue,omitempty" bson:"retail_price_value,omitempty"`
	PurchasePrice    int64    `json:"purchasePrice,omitempty" bson:"purchase_price,omitempty"`
	LocationCodes    []string `json:"locationCodes,omitempty"`

	VAT float64 `json:"vat,omitempty" bson:"vat,omitempty"`
}

type CreateSkuResponse struct {
	Message string `json:"message"`
	Status  string `json:"status"`
}

type Sku struct {
	ItemCode string `json:"itemCode,omitempty"`
	SKU      string `json:"sku,omitempty"`
}
type baseResponse[T any] struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Data    []T    `json:"data"`
}
