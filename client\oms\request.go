package oms

import "time"

// OrderCreateEvent ...
type OrderCreateEvent struct {
	Channel            string `json:"channel"`
	OrderID            int64  `json:"orderID"`
	OrderCode          string `json:"orderCode"`
	CreatedByAccountId int64  `json:"createdByAccountId"`

	AccountID               int64   `json:"accountId"`
	CustomerID              int64   `json:"customerId"`
	CustomerCode            string  `json:"customerCode"`
	CustomerName            string  `json:"customerName"`
	CustomerPhone           string  `json:"customerPhone"`
	CustomerEmail           *string `json:"customerEmail"`
	CustomerShippingAddress string  `json:"customerShippingAddress"`
	CustomerDistrictCode    string  `json:"customerDistrictCode"`
	CustomerWardCode        string  `json:"customerWardCode"`
	CustomerProvinceCode    string  `json:"customerProvinceCode"`
	CustomerRegionCode      string  `json:"customerRegionCode"`

	Price                       int64      `json:"price"`
	Cod                         *int64     `json:"cod"`
	PaymentMethod               string     `json:"paymentMethod"`
	PaymentMethodPercentage     *float64   `json:"paymentMethodPercentage"`
	PaymentMethodDiscountAmount int64      `json:"paymentMethodDiscountAmount"`
	DeliveryMethod              string     `json:"deliveryMethod"`
	DeliveryMethodFee           *float64   `json:"deliveryMethodFee"`
	DeliveryDate                *time.Time `json:"deliveryDate"`
	ConfirmationDate            *time.Time `json:"confirmationDate"`
	Source                      string     `json:"source"`
	Note                        *string    `json:"note"`
	ConfirmType                 string     `json:"confirmType"`
	RedeemCode                  *[]string  `json:"redeemCode"`
	VoucherAmount               int64      `json:"voucherAmount"`
	ExtraFee                    *int64     `json:"extraFee"`
	TotalPrice                  *int       `json:"totalPrice"`
	TotalDiscount               *int       `json:"totalDiscount"`
	TotalFee                    *int       `json:"totalFee"`
	Tags                        []string   `json:"tags"`
	// OrderItems                  []*model.OrderItem        `json:"orderItems"`

	// warehouse
	IsSplitDeliveryOrder *bool `json:"isSplitDeliveryOrder"`

	// invoice
	CanExportInvoice *bool           `json:"canExportInvoice"`
	Invoice          *InvoiceRequest `json:"invoice"`

	InternalOrderType string `json:"internalOrderType"`
	SystemDisplay     string `json:"systemDisplay"`
}

type InvoiceRequest struct {
	CompanyName    string `json:"companyName,omitempty"`
	TaxCode        string `json:"taxCode,omitempty"`
	CompanyAddress string `json:"companyAddress,omitempty"`
	Email          string `json:"email,omitempty"`
	InvoiceRequest *bool  `json:"invoiceRequest,omitempty"`
}

type GenOrderIDResponse struct {
	ID   int64  `json:"id"`
	Code string `json:"code"`
}

type CancelOrderRequest struct {
	OrderID     int64  `json:"orderID"`
	OrderCode   string `json:"orderCode"`
	Note        string `json:"note"`
	AccountName string `json:"accountName"`
	AccountID   int64  `json:"accountID"`
}

type InvoiceExchangeRequest struct {
	OrderID int64 `json:"orderID"`
}
