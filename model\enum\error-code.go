package enum

type ErrorCodeValue string

type errorCodeInvalidValue struct {
	// Common
	ParseData ErrorCodeValue
	Action    ErrorCodeValue

	PermissionDenied ErrorCodeValue
}

var ErrorCodeInvalid = &errorCodeInvalidValue{
	// Common
	ParseData: "INVALID_PARSE_DATA",
	Action:    "INVALID_ACTION",

	PermissionDenied: "PERMISSION_DENIED",
}

type ErrorMessageValue string
type errorMessage struct {
	PermissionDenied ErrorMessageValue
}

var ErrorMessage = &errorMessage{
	PermissionDenied: "Tài khoản của bạn không thể thực hiện thao tác này",
}
