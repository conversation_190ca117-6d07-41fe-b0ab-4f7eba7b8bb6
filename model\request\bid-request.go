package request

import (
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/enum"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
)

// CreateBidRequest ...
type CreateBidRequest struct {
	InvitationOfBid             string     `json:"invitationOfBid"`
	BidName                     string     `json:"bidName"`
	ProcuringEntity             string     `json:"procuringEntity"`
	Address                     string     `json:"address"`
	Contact                     string     `json:"contact"`
	ContractExecutionPeriod     int64      `json:"contractExecutionPeriod"`
	ContractExecutionPeriodKind string     `json:"contractExecutionPeriodKind,omitempty"`
	ContractTerminationDate     *time.Time `json:"contractTerminationDate,omitempty"`

	Region                      string     `json:"region"`
	OpeningDate                 *time.Time `json:"openingDate"`
	ClosingDate                 *time.Time `json:"closingDate"`
	SecurityValidity            int64      `json:"securityValidity"`
	PerformanceValidity         string     `json:"performanceValidity"`
	SecuritySubmissionMethod    string     `json:"securitySubmissionMethod"`
	SecuritySubmissionAmount    int64      `json:"securitySubmissionAmount"`
	SecuritySubmissionDate      *time.Time `json:"securitySubmissionDate"`
	PerformanceSubmissionMethod string     `json:"performanceSubmissionMethod"`
	PerformanceSubmissionAmount int64      `json:"performanceSubmissionAmount"`
	PerformanceSubmissionDate   *time.Time `json:"performanceSubmissionDate"`
	MainCategory                string     `json:"mainCategory"`
	Attachments                 *[]string  `json:"attachments,omitempty" bson:"attachments,omitempty"`
	Year                        int        `json:"year,omitempty" bson:"year,omitempty"`
}

func (m *CreateBidRequest) Validate() *common.APIResponse[any] {
	if len(m.InvitationOfBid) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invitation Of Bid is required",
			ErrorCode: "INVALID_ITB",
		}
	}

	if len(m.BidName) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Bid name is required",
			ErrorCode: "INVALID_BID_NAME",
		}
	}

	if len(m.ProcuringEntity) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Procuring entity is required",
			ErrorCode: "INVALID_PROCURING_ENTITY",
		}
	}

	if m.Year <= 2020 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Year is required",
			ErrorCode: "INVALID_YEAR",
		}
	}

	if len(m.MainCategory) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Main category is required",
			ErrorCode: "INVALID_CATEGORY",
		}
	}

	if m.OpeningDate != nil && m.ClosingDate != nil {
		if m.ClosingDate.Before(*m.OpeningDate) {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   "Closing date must be before opening date",
				ErrorCode: "INVALID_DATE",
			}
		}
	}

	if m.SecurityValidity <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Security validity must be greater than zero",
			ErrorCode: "INVALID_SEC_VALIDITY",
		}
	}

	// if m.PerformanceValidity <= 0 {
	// 	return &common.APIResponse[any]{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Performance validity must be greater than zero",
	// 		ErrorCode: "INVALID_PER_VALIDITY",
	// 	}
	// }

	if len(m.SecuritySubmissionMethod) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Security submission method is required",
			ErrorCode: "INVALID_SEC_SUB_METHOD",
		}
	}

	if m.SecuritySubmissionMethod != enum.BidSubmissionMethod.CASH &&
		m.SecuritySubmissionMethod != enum.BidSubmissionMethod.LETTER_OF_BANK_GUARANTEE {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Security submission method is invalid",
			ErrorCode: "INVALID_SEC_SUB_METHOD",
		}
	}

	// if len(m.PerformanceSubmissionMethod) == 0 {
	// 	return &common.APIResponse[any]{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Performance submission method is required",
	// 		ErrorCode: "INVALID_SEC_SUB_METHOD",
	// 	}
	// }

	// if m.PerformanceSubmissionMethod != enum.BidSubmissionMethod.CASH &&
	// 	m.PerformanceSubmissionMethod != enum.BidSubmissionMethod.LETTER_OF_BANK_GUARANTEE {
	// 	return &common.APIResponse[any]{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Performance submission method is invalid",
	// 		ErrorCode: "INVALID_PER_SUB_METHOD",
	// 	}
	// }

	if m.ContractExecutionPeriod <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Contract execution period must be greater than zero",
			ErrorCode: "INVALID_EXECUTION_PERIOD",
		}
	}

	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}

// UpdateBidRequest ...
type UpdateBidRequest struct {
	BidID                       int64      `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	Status                      *string    `json:"status,omitempty" bson:"status,omitempty"`
	WinningDecision             *string    `json:"winningDecision,omitempty" bson:"winning_decision,omitempty"`
	ContractNo                  *string    `json:"contractNo,omitempty" bson:"contract_no,omitempty"`
	Contact                     *string    `json:"contact,omitempty" bson:"contact,omitempty"`
	Attachments                 *[]string  `json:"attachments,omitempty" bson:"attachments,omitempty"`
	Region                      *string    `json:"region,omitempty" bson:"region,omitempty"`
	ContractTermination         *time.Time `json:"contractTermination,omitempty"`
	ContractExecutionPeriodKind *string    `json:"contractExecutionPeriodKind,omitempty"`
	ContractExecutionPeriod     *int64     `json:"contractExecutionPeriod,omitempty"`

	Year                        *int       `json:"year,omitempty"`
	Address                     *string    `json:"address,omitempty"`
	BidName                     *string    `json:"bidName,omitempty"`
	ProcuringEntity             *string    `json:"procuringEntity,omitempty"`
	PerformanceValidity         *string    `json:"performanceValidity,omitempty"`
	PerformanceSubmissionMethod *string    `json:"performanceSubmissionMethod,omitempty"`
	PerformanceSubmissionAmount *int64     `json:"performanceSubmissionAmount,omitempty"`
	PerformanceSubmissionDate   *time.Time `json:"performanceSubmissionDate,omitempty"`
	SecurityValidity            *int64     `json:"securityValidity,omitempty"`
	SecuritySubmissionMethod    *string    `json:"securitySubmissionMethod,omitempty"`
	SecuritySubmissionAmount    *int64     `json:"securitySubmissionAmount,omitempty"`
	SecuritySubmissionDate      *time.Time `json:"securitySubmissionDate,omitempty"`
	OpeningDate                 *time.Time `json:"openingDate,omitempty"`
	ClosingDate                 *time.Time `json:"closingDate,omitempty"`
	MainCategory                string     `json:"mainCategory,omitempty"`
}

// BidQuery ...
type BidQuery struct {
	BidIDs                      []int64    `json:"bidIDs,omitempty" bson:"-"`
	InvitationOfBid             string     `json:"invitationOfBid,omitempty" bson:"itb,omitempty"`
	ProcuringEntity             string     `json:"procuringEntity,omitempty" bson:"-"`
	Status                      string     `json:"status,omitempty" bson:"status,omitempty"`
	PerformanceSubmissionMethod string     `json:"performanceSubmissionMethod,omitempty" bson:"performance_submission_method,omitempty"`
	Year                        int        `json:"year,omitempty" bson:"year,omitempty"`
	Region                      string     `json:"region,omitempty" bson:"region,omitempty"`
	CreatedFrom                 *time.Time `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo                   *time.Time `json:"createdTo,omitempty" bson:"-"`
	ComplexQuery                []*bson.M  `json:"-" bson:"$and,omitempty"`
}
