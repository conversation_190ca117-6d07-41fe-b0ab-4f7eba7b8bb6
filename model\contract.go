package model

import (
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// ContractStatusValue ...
type ContractStatusValue string

// ContractStatusEnt ...
type ContractStatusEnt struct {
	DRAFT  ContractStatusValue
	ACTIVE ContractStatusValue
	FINISH ContractStatusValue
	DONE   ContractStatusValue
}

// ContractStatus ...
var ContractStatus = &ContractStatusEnt{
	"DRAFT",
	"ACTIVE",
	"FINISH",
	"DONE",
}

// Contract ...
type Contract struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	Code            string             `json:"code,omitempty" bson:"code,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64  `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64  `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64  `json:"entityID,omitempty" bson:"entity_id,omitempty"`

	// optional
	BidID           *int64  `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	InvitationOfBid *string `json:"itb,omitempty" bson:"itb,omitempty"`

	ContractID              int64               `json:"contractID,omitempty" bson:"contract_id,omitempty"`
	Name                    string              `json:"name,omitempty" bson:"name,omitempty"`
	ContractNumber          string              `json:"contractNumber,omitempty" bson:"contract_number,omitempty"`
	ContractType            string              `json:"contractType,omitempty" bson:"contract_type,omitempty"`
	ContractValue           int64               `json:"contractValue,omitempty" bson:"contract_value,omitempty"`
	CurrentAmount           int64               `json:"currentAmount,omitempty" bson:"current_amount,omitempty"`
	NumOfProduct            int64               `json:"numOfProduct,omitempty" bson:"num_of_product,omitempty"`
	MainCategory            string              `json:"mainCategory,omitempty" bson:"main_category,omitempty"`
	Email                   string              `json:"email,omitempty" bson:"email,omitempty"`
	BudgetUnitCode          *string             `json:"budgetUnitCode,omitempty" bson:"budget_unit_code,omitempty"`
	BeneficiaryID           int64               `json:"beneficiaryID,omitempty" bson:"beneficiary_id,omitempty"`
	BeneficiaryCode         string              `json:"beneficiaryCode,omitempty" bson:"beneficiary_code,omitempty"`
	BeneficiaryName         string              `json:"beneficiaryName,omitempty" bson:"beneficiary_name,omitempty"`
	DecisionNo              string              `json:"decisionNo,omitempty" bson:"decision_no,omitempty"`
	TaxCode                 string              `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	PhoneNumber             string              `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
	TermOfPayment           int64               `json:"termOfPayment,omitempty" bson:"term_of_payment,omitempty"`
	IsStoreDocument         *bool               `json:"isStoreContractDocument,omitempty" bson:"is_store_document,omitempty"`
	Address                 string              `json:"address,omitempty" bson:"address,omitempty"`
	SigningDate             *time.Time          `json:"signingDate,omitempty" bson:"signing_date,omitempty"`
	ExpireDate              *time.Time          `json:"expireDate,omitempty" bson:"expire_date,omitempty"`
	Status                  ContractStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	Content                 string              `json:"content,omitempty" bson:"content,omitempty"`
	Note                    string              `json:"note,omitempty" bson:"note,omitempty"`
	Attachments             []string            `json:"attachments,omitempty" bson:"attachments,omitempty"`
	ExtendAttachments       []string            `json:"extendAttachments,omitempty" bson:"extend_attachments,omitempty"`
	TraditionalContractCode *string             `json:"traditionalContractCode,omitempty" bson:"traditional_contract_code,omitempty"`
	HashTag                 string              `json:"-" bson:"hash_tag,omitempty"` // for search

	// legal
	LegalEntityCode        string  `json:"legalEntityCode,omitempty" bson:"legal_entity_code,omitempty"`
	LegalEntityName        string  `json:"legalEntityName,omitempty" bson:"legal_entity_name,omitempty"`
	LegalEntityTaxCode     string  `json:"legalEntityTaxCode,omitempty" bson:"legal_entity_tax_code,omitempty"`
	LegalEntityAddress     string  `json:"legalEntityAddress,omitempty" bson:"legal_entity_address,omitempty"`
	LegalEntityEmail       string  `json:"legalEntityEmail,omitempty" bson:"legal_entity_email,omitempty"`
	LegalEntityTel         string  `json:"legalEntityTel,omitempty" bson:"legal_entity_tel,omitempty"`
	LegalEntityBankCode    string  `json:"legalEntityBankCode,omitempty" bson:"legal_entity_bank_code,omitempty"`
	LegalEntityBankName    string  `json:"legalEntityBankName,omitempty" bson:"legal_entity_bank_name,omitempty"`
	LegalEntityBankBranch  string  `json:"legalEntityBankBranch,omitempty" bson:"legal_entity_bank_branch,omitempty"`
	LegalEntityBankAccount string  `json:"legalEntityBankAccount,omitempty" bson:"legal_entity_bank_account,omitempty"`
	AccountingRefCode      *string `json:"accountingRefCode,omitempty" bson:"accounting_ref_code,omitempty"`
	PaymentTerm            int64   `json:"paymentTerm,omitempty" bson:"payment_term,omitempty"`
	DebtLimit              int64   `json:"debtLimit,omitempty" bson:"debt_limit,omitempty"`
	LegalEntityBranch      string  `json:"legalEntityBranch,omitempty" bson:"legal_entity_branch,omitempty"`

	// ForQuery
	ComplexQuery []*bson.M                                  `json:"-" bson:"$and,omitempty"`
	BidInfo      *Bid                                       `json:"bidInfo,omitempty" bson:"-"`
	Debt         *request.ContractAccountingBalanceResponse `json:"debt,omitempty" bson:"-"`

	//Permission
	Scope []string `json:"-" bson:"scope,omitempty"` // Access scope
}

// ContractDB ...
var ContractDB = &mongodb.Instance[*Contract]{
	ColName: "contracts",
}

// InitContractModel ....
func InitContractModel(s *mongo.Database) {
	ContractDB.ApplyDatabase(s)

	t := true
	ContractDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	ContractDB.CreateIndex(bson.D{
		{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	ContractDB.CreateIndex(bson.D{
		{Key: "contract_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	ContractDB.CreateIndex(bson.D{
		{Key: "itb", Value: 1},
	}, &options.IndexOptions{})

	ContractDB.CreateIndex(bson.D{
		{Key: "contract_number", Value: 1},
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	ContractDB.CreateIndex(bson.D{
		{Key: "contract_number", Value: 1},
	}, &options.IndexOptions{})

	ContractDB.CreateIndex(bson.D{
		{Key: "created_time", Value: 1},
	}, &options.IndexOptions{})
}

// GenContractHashTag ...
func GenContractHashTag(contract *Contract) string {
	return strings.Replace(utils.NormalizeString(contract.Code+";"+contract.ContractNumber+";"+contract.Name), " ", "-", -1)
}

// IsValidContractStatus ...
func IsValidContractStatus(status string) bool {
	switch ContractStatusValue(status) {
	case ContractStatus.DRAFT, ContractStatus.ACTIVE, ContractStatus.DONE, ContractStatus.FINISH:
		{
			return true
		}
	}
	return false
}

type QueryProductListDataResponse struct {
	Products []*Product                                 `json:"products"`
	Debt     *request.ContractAccountingBalanceResponse `json:"debt"`
	Total    int64                                      `json:"total"`
}
