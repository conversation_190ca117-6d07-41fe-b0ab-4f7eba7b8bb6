package action

import (
	"fmt"
	"runtime/debug"

	"github.com/labstack/gommon/log"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/oms"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/product"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
)

func HandleCreateSku(item *db_queue.Item[*job.CreateSkuMsg]) error {
	defer func() {
		if r := recover(); r != nil {
			log.Info("panic on: ", r, string(debug.Stack()))
		}
	}()

	data := item.Data
	if data == nil {
		fmt.Printf("data nil with %v\n", item)
		return nil
	}

	// check trùng or thành công
	errSku := product.CreateSkuMain(data.Sku)
	if errSku != nil {
		fmt.Printf("CreateSkuMain error: %v\n", errSku)
		return nil
	}

	errSkuItem := product.CreateSkuItem(data.SkuItem)
	if errSkuItem != nil {
		fmt.Printf("CreateSkuItem error: %v\n", errSkuItem)
		return errSkuItem
	}

	return nil
}

func HandleSyncOrderToOms(item *db_queue.Item[*job.OrderOms]) error {
	defer func() {
		if r := recover(); r != nil {
			log.Info("panic on: ", r, string(debug.Stack()))
		}
	}()

	data := item.Data
	if data == nil {
		fmt.Printf("data nil with %v\n", item)
		return nil
	}

	err := oms.CreateOrder(data)
	if err != nil {
		return err
	}
	return nil
}

func HandleSyncContractToAccounting(item *db_queue.Item[*job.CreateContractAccountingRequest]) error {
	defer func() {
		if r := recover(); r != nil {
			log.Info("panic on: ", r, string(debug.Stack()))
		}
	}()

	data := item.Data
	if data == nil {
		fmt.Printf("data nil with %v\n", item)
		return nil
	}

	code, err := integration.CreateContractAccounting(data)
	if err != nil {
		return err
	}

	_ = model.ContractDB.UpdateOne(&model.Contract{
		ContractID: data.Data.ContractID,
	}, &model.Contract{
		AccountingRefCode: &code,
	})

	return nil
}
