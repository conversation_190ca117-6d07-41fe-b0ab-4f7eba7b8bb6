// Package api ...
package api

import (
	"strings"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// CreateProduct ...
func CreateProduct(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateProductRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource != nil {
		return resp.Respond(action.CreateProduct(&input, actionSource))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn không thể thực hiện thao tác này",
	})
}

// UpdateProduct ...
func UpdateProduct(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateProductRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.UpdateProduct(&input, actionSource))
}

// GetProductInfo ...
func GetProductInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	arrParams := strings.Split(params["option"], ",")
	optionContract := false
	for _, item := range arrParams {
		if item == "contract" {
			optionContract = true
			break
		}
	}

	if acc := utils.GetActionSource(req); acc != nil {
		return resp.Respond(action.GetProductInfo(params["code"], optionContract, acc))
	}

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Unauthorized,
		Message: "Tài khoản của bạn không thể thực hiện thao tác này",
	})
}

// QueryProductList ...
func QueryProductList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.ProductQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.QueryProductList(&input.Q, input.Search, input.Offset, input.Limit, input.Option, actionSource))
}

// SearchProductFuzzyWithProxy ...
func SearchProductFuzzyWithProxy(req server.APIRequest, resp server.APIResponder) error {
	var input request.ProductFuzzySearchQuery
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.SearchProductFuzzyWithProxy(&input))
}
