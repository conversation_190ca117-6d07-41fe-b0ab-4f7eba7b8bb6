package action

import (
	"fmt"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
)

func applyOrder(_ string, _ []byte, resp *common.APIResponse[*model.Order]) *common.APIResponse[any] {
	// trigger debt transaction
	if resp.Ok() {
		orderInfo := resp.Data[0]

		// get contract info
		contractResp := model.ContractDB.QueryOne(&model.Contract{
			Code:   orderInfo.ContractCode,
			Status: model.ContractStatus.ACTIVE,
		})

		if !contractResp.Ok() {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Error,
				Message:   "Order contract not found." + contractResp.Message,
				ErrorCode: "ORDER_CONTRACT_NOT_FOUND",
			}
		}

		orderContractInfo := contractResp.Data[0]

		newTrans := &integration.CreateContractTransactionRequest{
			Channel:                 fmt.Sprintf("%s_VN", orderContractInfo.LegalEntityBranch),
			CountryCode:             "VN",
			OrderID:                 int(orderInfo.OrderID),
			OrderCode:               orderInfo.OrderCode,
			CustomerName:            orderInfo.ReceiverName,
			DeliverMethod:           orderInfo.DeliveryMethod,
			Price:                   int(orderInfo.TotalAmount),
			Currency:                orderInfo.Currency,
			CustomerID:              int(orderInfo.ReceiverID),
			Tags:                    []string{fmt.Sprintf("%s_VN", orderContractInfo.LegalEntityBranch)},
			CreatedTime:             orderInfo.CreatedTime,
			CustomerType:            "TENDER",
			TotalPrice:              int(orderInfo.TotalAmount),
			CustomerDistrictCode:    orderInfo.DistrictCode,
			CustomerWardCode:        orderInfo.WardCode,
			InternalOrderID:         orderInfo.OrderID,
			InternalOrderCode:       orderInfo.OrderCode,
			Status:                  orderInfo.Status,
			AccountID:               orderInfo.AccountID,
			PaymentMethod:           orderInfo.PaymentMethod,
			Source:                  fmt.Sprintf("INTERNAL-%s", orderContractInfo.LegalEntityBranch),
			CustomerShippingAddress: orderInfo.ReceiverShippingAddress,
			DocumentDataCode:        orderInfo.AccountingRefCode,
			CustomerCode:            orderInfo.ReceiverCode,
			CustomerPhone:           orderInfo.ReceiverPhoneNumber,
			CustomerProvinceCode:    orderInfo.ProvinceCode,
			PartyBCode:              orderInfo.ReceiverCode,
		}

		fmt.Printf("%v\n", newTrans)

		_, err := integration.CreateContractTransaction(newTrans)
		if err != nil {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Error,
				Message:   "The order submit transaction debt failed, please contact the admin. " + err.Error(),
				ErrorCode: "DEBT_TRANSACTION_ERROR",
			}
		}
	}

	return &common.APIResponse[any]{
		Status:    resp.Status,
		Message:   resp.Message,
		ErrorCode: resp.ErrorCode,
		Data:      utils.ConvertToAny(resp.Data),
	}
}
