package integration

import (
	"errors"
	"net/http"

	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
)

type ConfirmContractDocument struct {
	Links []string            `json:"links"`
	Refs  []map[string]string `json:"refs"`
}

// MakeConfirmContractDocument ...
func MakeConfirmContractDocument(payload *ConfirmContractDocument) error {
	resp, err := contractClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, contractClient.headers, nil, payload, pathConfirmContractDocument)
	if err != nil {
		return err
	}

	if resp.Code != http.StatusOK {
		return errors.New("error: fail to call create document contract")
	}

	return nil
}
