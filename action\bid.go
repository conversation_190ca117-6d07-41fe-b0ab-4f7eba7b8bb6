package action

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// QueryBidList ...
func QueryBidList(query *request.BidQuery, search string, offset, limit int64, queryOptions request.QueryOption) common.Response {
	if len(query.Status) > 0 && !model.IsValidBidStatus(query.Status) {
		return utils.InvalidResponse("Invalid status", "INVALID_STATUS")
	}

	if len(query.BidIDs) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"bid_id": bson.M{
				"$in": utils.Unique(query.BidIDs),
			},
		})
	}

	if len(search) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", regexp.QuoteMeta(utils.ParserQ(search))), Options: ""},
		})
	} else {
		if len(query.ProcuringEntity) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
				"hash_tag_entity": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", regexp.QuoteMeta(utils.ParserQ(query.ProcuringEntity))), Options: ""},
			})
		}
	}

	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": query.CreatedTo,
			},
		})
	}

	if query.Status == string(model.BidStatus.EXPIRED) {
		// reset field
		query.Status = ""
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"contract_termination_date": bson.M{
				"$lte": time.Now(),
			},
		})
	}

	if len(query.PerformanceSubmissionMethod) > 0 {
		query.PerformanceSubmissionMethod = strings.ToUpper(query.PerformanceSubmissionMethod)
	}

	resp := model.BidDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: -1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.BidDB.Count(query).Total
	}
	return resp
}

// CreateBid ...
func CreateBid(input *request.CreateBidRequest, actionSource *request.ActionSource) common.Response {
	if checkResponse := input.Validate(); !checkResponse.Ok() {
		return checkResponse
	}

	bidInfo := &model.Bid{
		InvitationOfBid:          input.InvitationOfBid,
		BidName:                  input.BidName,
		ProcuringEntity:          input.ProcuringEntity,
		MainCategory:             input.MainCategory,
		OpeningDate:              input.OpeningDate,
		ClosingDate:              input.ClosingDate,
		SecurityValidity:         input.SecurityValidity,
		PerformanceValidity:      input.PerformanceValidity,
		SecuritySubmissionMethod: strings.ToUpper(input.SecuritySubmissionMethod),
		ContractExecutionPeriod:  input.ContractExecutionPeriod,
		Region:                   input.Region,
		Address:                  input.Address,
		Contact:                  input.Contact,
		Year:                     input.Year,
	}

	if input.ContractExecutionPeriodKind != "" {
		bidInfo.ContractExecutionPeriodKind = input.ContractExecutionPeriodKind
	}

	if input.ContractTerminationDate != nil {
		bidInfo.ContractTerminationDate = input.ContractTerminationDate
	}

	if input.ContractExecutionPeriodKind != "" {
		bidInfo.ContractExecutionPeriodKind = input.ContractExecutionPeriodKind
	}

	if input.ContractTerminationDate != nil {
		bidInfo.ContractTerminationDate = input.ContractTerminationDate
	}

	if bidInfo.SecuritySubmissionMethod == "CASH" {
		bidInfo.SecuritySubmissionAmount = input.SecuritySubmissionAmount
		bidInfo.SecuritySubmissionDate = input.SecuritySubmissionDate
	}

	// if bidInfo.PerformanceSubmissionMethod == "CASH" {
	// 	bidInfo.PerformanceSubmissionAmount = input.PerformanceSubmissionAmount
	// 	bidInfo.PerformanceSubmissionDate = input.PerformanceSubmissionDate
	// }

	bidInfo.HashTag = model.GenBidHashTag(bidInfo)
	bidInfo.HashTagEntity = model.GenProcuringEntityHashTag(bidInfo)

	if actionSource != nil {
		if actionSource.Account != nil {
			bidInfo.CreatedByID = actionSource.Account.AccountID
			bidInfo.CreatedByName = actionSource.Account.FullName
		}

		if actionSource.Session != nil {
			bidInfo.OrgID = actionSource.Session.OrgID
			bidInfo.EntityID = actionSource.Session.EntityID
		}
	}

	if input.Attachments != nil && len(*input.Attachments) > 0 {
		bidInfo.Attachments = input.Attachments
	}

	bidInfo.BidID = model.GenBidID()
	bidInfo.Code = model.ConvertToCode(bidInfo.BidID)
	bidInfo.Status = model.BidStatus.WAITING

	bidResp := model.BidDB.Create(bidInfo)
	if !bidResp.Ok() {
		return bidResp
	}

	if bidInfo.Attachments != nil && len(*bidInfo.Attachments) > 0 {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: *bidInfo.Attachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": bidInfo.InvitationOfBid,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	return bidResp
}

// GetBidInfo ...
func GetBidInfo(bidID int64) common.Response {
	if bidID <= 0 {
		return utils.InvalidResponse("Invalid BidID", "INVALID_BID_ID")
	}
	resp := model.BidDB.QueryOne(&model.Bid{
		BidID: bidID,
	})
	return resp
}

// UpdateBid ...
func UpdateBid(input *request.UpdateBidRequest, actionSource *request.ActionSource) common.Response {
	if input.BidID <= 0 {
		return utils.InvalidResponse("Invalid BidID", "INVALID_BID_ID")
	}
	bidResp := model.BidDB.QueryOne(&model.Bid{
		BidID: input.BidID,
	})

	if !bidResp.Ok() {
		return utils.NotfoundDataResponse[model.Bid]("Not found any matched bid", "BID_NOT_FOUND")
	}

	bid := bidResp.Data[0]

	updater := &model.Bid{}
	if input.Status != nil && model.IsValidBidStatus(*input.Status) && string(bid.Status) != *input.Status {
		updater.Status = model.BidStatusValue(*input.Status)
		if bid.Status != model.BidStatus.WIN && updater.Status == model.BidStatus.WIN {
			queryLotExist := model.LotDB.QueryOne(&model.Lot{
				BidID:  bid.BidID,
				Status: model.LotStatus.WAITING,
			})
			if queryLotExist.Ok() {
				return utils.ExistedResponse("Please update the results of all bidding products.", "LOT_INVALID")
			}
		}
	}

	if input.WinningDecision != nil && *input.WinningDecision != "" {
		updater.WinningDecision = input.WinningDecision
	}

	if input.ContractNo != nil && *input.ContractNo != "" {
		updater.ContractNo = input.ContractNo
	}

	if input.Attachments != nil && len(*input.Attachments) > 0 {
		updater.Attachments = input.Attachments
	}

	if input.Contact != nil && len(*input.Contact) > 0 {
		updater.Contact = *input.Contact
	}

	if input.Region != nil && len(*input.Region) > 0 {
		updater.Region = *input.Region
	}

	if input.ContractTermination != nil {
		updater.ContractTerminationDate = input.ContractTermination
	}

	if input.Address != nil && len(*input.Address) > 0 && *input.Address != bid.Address {
		updater.Address = *input.Address
	}

	if input.BidName != nil && len(*input.BidName) > 0 && *input.BidName != bid.BidName {
		updater.BidName = *input.BidName
	}

	if input.ProcuringEntity != nil && len(*input.ProcuringEntity) > 0 && *input.ProcuringEntity != bid.ProcuringEntity {
		updater.ProcuringEntity = *input.ProcuringEntity
	}

	if input.PerformanceValidity != nil && len(*input.PerformanceValidity) > 0 {
		updater.PerformanceValidity = *input.PerformanceValidity
	}

	if input.PerformanceSubmissionMethod != nil && len(*input.PerformanceSubmissionMethod) > 0 {
		updater.PerformanceSubmissionMethod = *input.PerformanceSubmissionMethod
	}

	if input.PerformanceSubmissionAmount != nil && *input.PerformanceSubmissionAmount > 0 {
		updater.PerformanceSubmissionAmount = *input.PerformanceSubmissionAmount
	}

	if input.PerformanceSubmissionDate != nil {
		updater.PerformanceSubmissionDate = input.PerformanceSubmissionDate
	}

	if input.SecurityValidity != nil && *input.SecurityValidity > 0 {
		updater.SecurityValidity = *input.SecurityValidity
	}

	if input.SecuritySubmissionAmount != nil && *input.SecuritySubmissionAmount != bid.SecuritySubmissionAmount {
		updater.SecuritySubmissionAmount = *input.SecuritySubmissionAmount
	}

	if input.SecuritySubmissionMethod != nil {
		updater.SecuritySubmissionMethod = *input.SecuritySubmissionMethod
	}

	if input.SecuritySubmissionDate != nil {
		updater.SecuritySubmissionDate = input.SecuritySubmissionDate
	}

	if input.OpeningDate != nil {
		updater.OpeningDate = input.OpeningDate
	}

	if input.ClosingDate != nil {
		updater.ClosingDate = input.ClosingDate
	}

	if input.ContractExecutionPeriod != nil {
		updater.ContractExecutionPeriod = *input.ContractExecutionPeriod
	}

	if input.ContractExecutionPeriodKind != nil {
		updater.ContractExecutionPeriodKind = *input.ContractExecutionPeriodKind
	}

	if input.Year != nil && *input.Year > 0 {
		updater.Year = *input.Year
	}

	if len(input.MainCategory) > 0 {
		updater.MainCategory = input.MainCategory
	}

	if len(bid.HashTagEntity) == 0 {
		updater.HashTagEntity = model.GenProcuringEntityHashTag(bid)
	}

	resp := model.BidDB.UpdateOne(model.Bid{
		BidID: bid.BidID,
	}, updater)
	if !resp.Ok() {
		return resp
	}
	if input.Attachments != nil &&
		len(*input.Attachments) > 0 &&
		bid.Attachments != nil &&
		utils.CompareArrays(*bid.Attachments, *input.Attachments) {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: *input.Attachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": bid.InvitationOfBid,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	return resp
}
