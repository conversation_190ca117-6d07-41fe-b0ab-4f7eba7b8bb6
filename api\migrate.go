package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

func MigrateLot(req server.APIRequest, resp server.APIResponder) error {
	return resp.Respond(utils.OkResponse("Ok", []interface{}{true}))
}

// MigrateContractDebt ...
func MigrateContractDebt(req server.APIRequest, resp server.APIResponder) error {
	var input request.MigrateContractDebtRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.MigrateContractDebt(input.ContractCode))
}

func MigrateContractQuantity(req server.APIRequest, resp server.APIResponder) error {
	var input request.MigrateContractDebtRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.MigrateContractQuantity(input.ContractCode))
}

func MigrateContractLegalEntityBranch(req server.APIRequest, resp server.APIResponder) error {
	go action.MigrateContractLegalEntityBranch()

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Ok,
		Message: "MigrateContractLegalEntityBranch is running",
	})
}

func MigrateScope(req server.APIRequest, resp server.APIResponder) error {
	go action.MigrateContractScope()
	go action.MigrateProductScope()
	go action.MigrateBeneficiaryScope()
	go action.MigrateOrderScope()

	return resp.Respond(&common.APIResponse[any]{
		Status:  common.APIStatus.Ok,
		Message: "Migrate scope is running",
	})
}
