package model

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// BidStatusValue ...
type BidStatusValue string

// BidStatus ...
type BidStatusEnt struct {
	WIN     BidStatusValue
	FAIL    BidStatusValue
	CANCEL  BidStatusValue
	WAITING BidStatusValue
	EXPIRED BidStatusValue
}

// BidStatus ...
var BidStatus = &BidStatusEnt{
	"WIN",
	"FAIL",
	"CANCEL",
	"WAITING",
	"EXPIRED",
}

// Bid ...
type Bid struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string         `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64          `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string         `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64          `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64          `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64          `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	Status        BidStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	BidID           int64      `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	Code            string     `json:"code,omitempty" bson:"code,omitempty"`
	InvitationOfBid string     `json:"itb,omitempty" bson:"itb,omitempty"`
	Year            int        `json:"year,omitempty" bson:"year,omitempty"`
	ProcuringEntity string     `json:"procuringEntity,omitempty" bson:"procuring_entity,omitempty"`
	BidName         string     `json:"bidName,omitempty" bson:"bid_name,omitempty"`
	BidPrice        int64      `json:"bidPrice,omitempty" bson:"bid_price,omitempty"`
	NumOfProduct    int64      `json:"numOfProduct,omitempty" bson:"num_of_product,omitempty"`
	MainCategory    string     `json:"mainCategory,omitempty" bson:"main_category,omitempty"`
	Region          string     `json:"region,omitempty" bson:"region,omitempty"`
	Address         string     `json:"address,omitempty" bson:"address,omitempty"`
	Contact         string     `json:"contact,omitempty" bson:"contact,omitempty"`
	OpeningDate     *time.Time `json:"openingDate,omitempty" bson:"opening_date,omitempty"`
	ClosingDate     *time.Time `json:"closingDate,omitempty" bson:"closing_date,omitempty"`
	Attachments     *[]string  `json:"attachments,omitempty" bson:"attachments,omitempty"`

	WinningQuantity *int64  `json:"winningQuantity,omitempty" bson:"winning_quantity,omitempty"`
	WinningPrice    *int64  `json:"winningPrice,omitempty" bson:"winning_price,omitempty"`
	WinningDecision *string `json:"winningDecision,omitempty" bson:"winning_decision,omitempty"`
	ContractNo      *string `json:"contractNo,omitempty" bson:"contract_no,omitempty"`

	SecurityValidity            int64      `json:"securityValidity,omitempty" bson:"security_validity,omitempty"`
	PerformanceValidity         string     `json:"performanceValidity,omitempty" bson:"performance_validity,omitempty"`
	SecuritySubmissionMethod    string     `json:"securitySubmissionMethod,omitempty" bson:"security_submission_method,omitempty"`
	PerformanceSubmissionMethod string     `json:"performanceSubmissionMethod,omitempty" bson:"performance_submission_method,omitempty"`
	ContractExecutionPeriod     int64      `json:"contractExecutionPeriod,omitempty" bson:"contract_execution_period,omitempty"`
	ContractExecutionPeriodKind string     `json:"contractExecutionPeriodKind,omitempty" bson:"contract_period_kind,omitempty"`
	ContractTerminationDate     *time.Time `json:"contractTerminationDate,omitempty" bson:"contract_termination_date,omitempty"`

	SecuritySubmissionAmount    int64      `json:"securitySubmissionAmount,omitempty" bson:"security_submission_amount,omitempty"`
	SecuritySubmissionDate      *time.Time `json:"securitySubmissionDate,omitempty" bson:"security_submission_date,omitempty"`
	PerformanceSubmissionAmount int64      `json:"performanceSubmissionAmount,omitempty" bson:"performance_submission_amount,omitempty"`
	PerformanceSubmissionDate   *time.Time `json:"performanceSubmissionDate,omitempty" bson:"performance_submission_date,omitempty"`

	TraditionalContractCode string `json:"traditionalContractCode,omitempty" bson:"traditional_contract_code,omitempty"`
	HashTag                 string `json:"-" bson:"hash_tag,omitempty"`        // for search
	HashTagEntity           string `json:"-" bson:"hash_tag_entity,omitempty"` // for search ProcuringEntity

	// ForQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// BidDB ...
var BidDB = &mongodb.Instance[*Bid]{
	ColName: "bids",
}

// InitBidModel ....
func InitBidModel(s *mongo.Database) {
	BidDB.ApplyDatabase(s)

	t := true
	BidDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	BidDB.CreateIndex(bson.D{
		{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	BidDB.CreateIndex(bson.D{
		{Key: "itb", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	BidDB.CreateIndex(bson.D{
		{Key: "status", Value: 1},
	}, &options.IndexOptions{})

	BidDB.CreateIndex(bson.D{
		{Key: "closing_date", Value: 1},
		{Key: "status", Value: 1},
	}, &options.IndexOptions{})

	BidDB.CreateIndex(bson.D{
		{Key: "created_time", Value: 1},
	}, &options.IndexOptions{})

	// 17May24
	BidDB.CreateIndex(bson.D{
		{Key: "year", Value: 1},
	}, &options.IndexOptions{})

	BidDB.CreateIndex(bson.D{
		{Key: "region", Value: 1},
	}, &options.IndexOptions{})

	BidDB.CreateIndex(bson.D{
		{Key: "performance_submission_method", Value: 1},
	}, &options.IndexOptions{})
}

// GenBidHashTag ...
func GenBidHashTag(bid *Bid) string {
	return fmt.Sprintf(
		"%s-%s",
		strings.Replace(strings.ToLower(bid.InvitationOfBid), "/", "-", -1),
		strings.Replace(utils.NormalizeString(bid.BidName), " ", "-", -1),
	)
}

func GenProcuringEntityHashTag(bid *Bid) string {
	return fmt.Sprintf(
		"%s-%s",
		strings.Replace(strings.ToLower(bid.InvitationOfBid), "/", "-", -1),
		strings.Replace(utils.NormalizeString(bid.ProcuringEntity), " ", "-", -1),
	)
}

// GenBeneficiaryHashTag ...
func GenBeneficiaryHashTag(beneficiary *Beneficiary) string {
	return fmt.Sprintf(
		"%s-%s-%s",
		strings.Replace(strings.ToLower(beneficiary.TaxCode), "/", "-", -1),
		strings.Replace(utils.NormalizeString(beneficiary.Name), " ", "-", -1),
		strings.Replace(utils.NormalizeString(beneficiary.PhoneNumber), " ", "-", -1),
	)
}

// IsValidBidStatus ...
func IsValidBidStatus(status string) bool {
	switch BidStatusValue(status) {
	case BidStatus.WIN, BidStatus.WAITING, BidStatus.FAIL, BidStatus.CANCEL, BidStatus.EXPIRED:
		{
			return true
		}
	}
	return false
}
