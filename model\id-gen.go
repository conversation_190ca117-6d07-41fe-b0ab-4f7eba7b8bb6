package model

import (
	"context"
	"math"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	KG_BID_ID         = "BID_ID"
	KG_LOT_ID         = "LOT_ID"
	KG_CONTRACT_ID    = "CONTRACT_ID"
	KG_PRODUCT_ID     = "PRODUCT_ID"
	KG_ORDER_ID       = "ORDER_ID"
	KG_ORDER_ITEM_ID  = "ORDER_ITEM_ID"
	KG_BENEFICIARY_ID = "KG_BENEFICIARY_ID"
	TEMPLATE          = "123456789qwertyuipasdfghjklzxcvbnmQWERTYUIPASDFGHJKLZXCVBNM"
)

// IdGen DB entity for gen code
type IdGen struct {
	ID    string `json:"id,omitempty" bson:"_id,omitempty"`
	Value int64  `json:"value,omitempty" bson:"value,omitempty"`
}

// IdGenDB DB model for gen code
var IdGenDB = &mongodb.Instance[*IdGen]{
	ColName: "_id_gen",
}

// InitIdGenModel ...
func InitIdGenModel(s *mongo.Database) {
	IdGenDB.ApplyDatabase(s)
}

// convertToCode convert id from int to string
func convertToCode(number int64, length int64, template string) string {
	var result = ""
	var i = int64(0)
	var ln = int64(len(template))
	var capacity = int64(math.Pow(float64(ln), float64(length)))
	number = number % capacity
	for i < length {
		var cur = number % ln
		if i > 0 {
			cur = (cur + int64(result[i-1])) % ln
		}
		result = result + string(template[cur])
		number = number / ln
		i++
	}
	return result
}

// ConvertToCode ...
func ConvertToCode(number int64) string {
	return convertToCode(number, 8, TEMPLATE)
}

// DecodeFromCode ...
func DecodeFromCode(code string, template string) int64 {
	var number int64 = 0
	var ln = int64(len(template))
	for i := len(code) - 1; i >= 0; i-- {
		cur := strings.IndexByte(template, code[i])
		if i < len(code)-1 {
			prev := strings.IndexByte(template, code[i+1])
			cur = (cur - prev) % len(template)
			if cur < 0 {
				cur += len(template)
			}
		}
		number = number*ln + int64(cur)
	}
	return number
}

// GenBidID ...
func GenBidID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_BID_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}

// GenLotID ...
func GenLotID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_LOT_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}

// GenContractID ...
func GenContractID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_CONTRACT_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}

// GenBidProductID ...
func GenBidProductID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_PRODUCT_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}

// GenOrderID ...
func GenOrderID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_ORDER_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}

// GenOrderItemID ...
func GenOrderItemID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_ORDER_ITEM_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}

// GenBeneficiaryID ...
func GenBeneficiaryID() int64 {
	increResult := IdGenDB.IncreaseOneWithCtx(context.TODO(), IdGen{
		ID: KG_BENEFICIARY_ID,
	}, "value", 1)
	val := increResult.Data[0]
	return val.Value + 1
}
