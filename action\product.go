package action

import (
	"errors"
	"fmt"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/product"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/enum"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateProduct ...
func CreateProduct(input *request.CreateProductRequest, actionSource *request.ActionSource) common.Response {
	if input.LotID <= 0 {
		return utils.InvalidResponse("Invalid lot id", "INVALID_LOT_ID")
	}

	if input.ProductID <= 0 {
		return utils.InvalidResponse("Invalid product id", "INVALID_PRODUCT_ID")
	}

	if input.ContractID <= 0 {
		return utils.InvalidResponse("Invalid contract id", "INVALID_CONTRACT_ID")
	}

	contractResult := model.ContractDB.QueryOne(&model.Contract{ContractID: input.ContractID})
	if !contractResult.Ok() {
		return utils.NotfoundResponse("Contract not found", "CONTRACT_NOT_FOUND")
	}

	contractInfo := contractResult.Data[0]

	// permission check
	err := ValidateContractProductScope(*actionSource, contractInfo)
	if err != nil { // allow admin
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   "You can't create product with this legal entity",
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	lotResult := model.LotDB.QueryOne(&model.Lot{
		LotID:     input.LotID,
		ProductID: input.ProductID,
	})
	if !lotResult.Ok() {
		return utils.NotfoundResponse("Lot not found", "LOT_NOT_FOUND")
	}

	lotInfo := lotResult.Data[0]

	productInfo := &model.Product{
		LotID:            lotInfo.LotID,
		ProductID:        lotInfo.ProductID,
		ProductMapID:     lotInfo.ProductID,
		ProductCode:      lotInfo.ProductCode,
		ProductName:      lotInfo.LotName,
		ContractID:       contractInfo.ContractID,
		ContractCode:     contractInfo.Code,
		MainContractID:   contractInfo.ContractID,
		Price:            lotInfo.LotPrice,
		Currency:         "VND",
		Quantity:         input.Quantity,
		QuantitySold:     utils.AnyToPointer[int64](0),
		QuantityRemain:   input.Quantity,
		QuantityInitial:  input.Quantity,
		GuaranteeAmount:  input.GuaranteeAmount,
		Amount:           input.Quantity * lotInfo.LotPrice,
		GroupMedicine:    input.GroupMedicine,
		Unit:             lotInfo.Unit,
		RegistrationNo:   input.RegistrationNo,
		VAT:              lotInfo.VAT,
		ManufacturerName: lotInfo.ManufacturerName,
		OriginName:       lotInfo.OriginName,
		Volume:           lotInfo.Volume,
		ContractType:     "MAIN_CONTRACT",
		Scope:            contractInfo.Scope,
	}

	if actionSource != nil {
		if actionSource.Account != nil {
			productInfo.CreatedByID = actionSource.Account.AccountID
			productInfo.CreatedByName = actionSource.Account.FullName
		}
		if actionSource.Session != nil {
			productInfo.OrgID = actionSource.Session.OrgID
			productInfo.EntityID = actionSource.Session.EntityID
		}
	}

	// check product unit
	arrProductUnit := make([]*pim.ProductUnit, 0)
	arrProductUnit = append(arrProductUnit, &pim.ProductUnit{
		ProductID:    productInfo.ProductID,
		FromUnit:     productInfo.Unit,
		FromQuantity: 1,
	})
	arrProductUnit = utils.Unique(arrProductUnit)

	productUnits, err := pim.ExchangeUnit(arrProductUnit)
	if err != nil {
		return utils.InvalidResponse(err.Error(), "UNIT_NOT_FOUND")
	}

	mapProductUnit := make(map[int64]*pim.ProductUnitResponse)
	for _, item := range productUnits.Products {
		mapProductUnit[item.ProductID] = item
	}

	if mapProductUnit[productInfo.ProductID] == nil || len(mapProductUnit[productInfo.ProductID].Rate) != 2 {
		return utils.InvalidResponse(fmt.Sprintf("Product %d not match unit", productInfo.ProductID), "UNIT_NOT_FOUND")
	}

	rates := mapProductUnit[productInfo.ProductID].Rate

	// set price for  1 quantity
	retailPriceTmp := productInfo.Price * 1 * rates[1] / rates[0]

	job.JobCreateSkuToMarketplace.Push(&job.CreateSkuMsg{
		Sku: &product.SkuMainRequest{
			Code:        model.ConvertToCode(model.GenLotID()),
			SellerCode:  contractInfo.LegalEntityBranch,
			ProductCode: lotInfo.ProductCode,
			ProductID:   lotInfo.ProductID,
			Slug:        utils.NormalizeString(lotInfo.LotName), // random hash name
			Type:        "NORMAL",
			Status:      "NORMAL",
		},
		SkuItem: &product.SkuItemRequest{
			SKU:              fmt.Sprintf("%s.%s", contractInfo.LegalEntityBranch, lotInfo.ProductCode),
			SellerCode:       contractInfo.LegalEntityBranch,
			SellerClass:      "INTERNAL",
			ProductCode:      lotInfo.ProductCode,
			ProductID:        lotInfo.ProductID,
			Status:           "NORMAL",
			RetailPriceType:  "FIXED_PRICE",
			RetailPriceValue: retailPriceTmp + retailPriceTmp*10/100,
			PurchasePrice:    retailPriceTmp,
			VAT:              5,
			LocationCodes:    []string{"MIENTRUNG", "MIENBAC", "MIENNAM"},
		},
	}, &db_queue.ItemMetadata{
		Topic:     "create_sku_to_marketplace",
		ReadyTime: utils.AnyToPointer(time.Now()),
		UniqueKey: fmt.Sprintf("%s.%s", contractInfo.LegalEntityBranch, lotInfo.ProductCode),
		Keys: []string{
			fmt.Sprintf("%s.%s", contractInfo.LegalEntityBranch, lotInfo.ProductCode),
		},
	})

	productInfo.Code = model.ConvertToCode(model.GenBidProductID())
	// productInfo.Status = model.ProductStatus.DRAFT

	productResp := model.ProductDB.Create(productInfo)
	if !productResp.Ok() {
		return productResp
	}
	return productResp
}

// QueryProductList ...
func QueryProductList(query *request.ProductQuery, _ string, offset, limit int64, queryOptions request.QueryOption, actionSource *request.ActionSource) common.Response {
	if len(query.ContractCode) > 0 {
		queryContract := &model.Contract{
			Code: query.ContractCode,
		}

		respContract := model.ContractDB.QueryOne(queryContract)
		if !respContract.Ok() {
			return utils.InvalidResponse("Not found any matched contract", "CONTRACT_NOT_FOUND")
		}

		contract := respContract.Data[0]

		query.ContractCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"main_contract_id": contract.ContractID,
		})

		if len(query.ContractType) > 0 {
			// query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			// 	"contract_type": strings.TrimSpace(query.ContractType),
			// })
			query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
				"quantity_remain": bson.M{
					"$gt": 0,
				},
			})
		}
	} else {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"contract_type": "MAIN_CONTRACT",
		})
	}

	// permission check
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	resp := model.ProductDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: -1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.ProductDB.Count(query).Total
	}

	return resp
}

// GetProductInfo ...
func GetProductInfo(code string, optionContract bool, actionSource *request.ActionSource) common.Response {
	if code == "" {
		return utils.InvalidResponse("Invalid code", "INVALID_CODE")
	}

	queryProductInfo := &model.Product{
		Code: code,
	}

	// permission check
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		queryProductInfo.ComplexQuery = append(queryProductInfo.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	queryProduct := model.ProductDB.QueryOne(queryProductInfo)
	if !queryProduct.Ok() {
		return queryProduct
	}

	product := queryProduct.Data[0]

	queryContract := &model.Contract{
		Code: product.ContractCode,
	}

	respContract := model.ContractDB.QueryOne(queryContract)
	if !respContract.Ok() {
		return utils.InvalidResponse("Not found any matched contract", "CONTRACT_NOT_FOUND")
	}

	if optionContract {
		queryContract := model.ContractDB.QueryOne(&model.Contract{
			ContractID: product.MainContractID,
		})
		if queryContract.Ok() {
			product.Contract = queryContract.Data[0]
			queryProduct.Data = []*model.Product{product}
		}
	}
	return queryProduct
}

// SearchProductFuzzyWithProxy ...
func SearchProductFuzzyWithProxy(input *request.ProductFuzzySearchQuery) common.Response {
	seller := ""
	if input.IsHaveSku {
		seller = input.Branch
	}
	resp, err := product.SearchProductFuzzy(input.Text, seller, input.ProductIDs)
	if err != nil {
		return utils.ErrorResponse(err.Error(), "SEARCH_ERROR")
	}
	return &common.APIResponse[*product.ProductResponse]{
		Status: common.APIStatus.Ok,
		Data:   resp.Data,
		Total:  resp.Total,
	}
}

// UpdateProduct ...
func UpdateProduct(input *request.UpdateProductRequest, actionSource *request.ActionSource) common.Response {
	if input.Code == "" {
		return utils.InvalidResponse("Invalid Code", "INVALID_CODE")
	}

	productResp := model.ProductDB.QueryOne(&model.Product{
		Code: input.Code,
	})

	if !productResp.Ok() {
		return utils.NotfoundDataResponse[any]("Not found any matched product", "PRODUCT_NOT_FOUND")
	}

	productInfo := productResp.Data[0]

	// get product contract
	contractQuery := &model.Contract{
		Code: productInfo.ContractCode,
	}

	// Check scope for the action source
	scope := utils.GenQueryScope(*actionSource)
	if scope == nil { // allow admin
	} else { // empty array => invalid
		contractQuery.ComplexQuery = append(contractQuery.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	contractResp := model.ContractDB.QueryOne(contractQuery)
	if !contractResp.Ok() {
		return utils.InvalidResponse("Not found any matched contract", "CONTRACT_NOT_FOUND")
	}

	contract := contractResp.Data[0]

	err := ValidateContractProductScope(*actionSource, contract)
	if err != nil {
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	updater := &model.Product{}

	if input.ProductID != nil && *input.ProductID > 0 {
		updater.ProductID = *input.ProductID
	}

	if input.Quantity != nil && *input.Quantity > 0 {
		updater.Quantity = *input.Quantity
	}

	if input.GroupMedicine != nil && *input.GroupMedicine > 0 {
		updater.GroupMedicine = *input.GroupMedicine
	}

	if input.RegistrationNo != nil && productInfo.RegistrationNo != *input.RegistrationNo {
		updater.RegistrationNo = *input.RegistrationNo
	}

	if input.Price > 0 {
		updater.Price = input.Price
		updater.Amount = productInfo.Quantity * updater.Price
	}

	return model.ProductDB.UpdateOne(model.Product{
		Code: productInfo.Code,
	}, updater)
}

// func validate create product
func ValidateContractProductScope(actionSource request.ActionSource, contract *model.Contract) error {
	if contract == nil || contract.LegalEntityCode == "" {
		return errors.New("contract legal entity is nil")
	}
	return utils.ValidateContractScope(actionSource, contract.LegalEntityCode)
}
