// Package action ...
package action

import (
	"fmt"
	"regexp"
	"strings"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// QueryLotList ...
func QueryLotList(query *request.LotQuery, search string, offset, limit int64, queryOptions request.QueryOption) common.Response {
	if search != "" {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", regexp.QuoteMeta(utils.ParserQ(search))), Options: ""},
		})
	}

	if len(query.LotIDs) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"lot_id": bson.M{
				"$in": utils.Unique(query.LotIDs),
			},
		})
	}

	resp := model.LotDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: 1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.LotDB.Count(query).Total
	}
	return resp
}

// GetLotInfo ...
func GetLotInfo(lotID int64) common.Response {
	if lotID <= 0 {
		return utils.InvalidResponse("Invalid lot id", "INVALID_LOT_ID")
	}
	resp := model.LotDB.QueryOne(&model.Lot{LotID: lotID})
	return resp
}

// UpdateLotInfo ...
func UpdateLotInfo(input *request.UpdateLotRequest, actionSource *request.ActionSource) common.Response {
	if input.LotID <= 0 {
		return utils.InvalidResponse("Invalid LotID", "INVALID_LOT_ID")
	}

	lotResp := model.LotDB.QueryOne(&model.Lot{
		LotID: input.LotID,
	})

	if !lotResp.Ok() {
		return utils.NotfoundDataResponse[any]("Not found any matched lot", "LOT_NOT_FOUND")
	}

	lotInfo := lotResp.Data[0]

	// if lotInfo.Status != model.LotStatus.WAITING {
	// 	return utils.NotfoundDataResponse[any]("Not found any matched lot", "LOT_INVALID")
	// }

	updater := &model.Lot{}
	isTriggerCount := false

	if input.Status != nil && model.IsValidLotStatus(*input.Status) {
		updater.Status = model.LotStatusValue(*input.Status)
		isTriggerCount = lotInfo.Status != updater.Status
	}

	if input.Status != nil {
		if lotInfo.Status != model.LotStatus.WIN &&
			*input.Status == string(model.LotStatus.WIN) &&
			lotInfo.ProductID <= 0 {
			return utils.InvalidResponse("Please update product map information with this lot", "INVALID_LOT_ID")
		}
	}

	if input.LotLineID != nil {
		updater.LotLineID = *input.LotLineID
	}

	if input.Volume != nil {
		updater.Volume = *input.Volume
	}

	if input.GuaranteeAmount != nil && *input.GuaranteeAmount != lotInfo.GuaranteeAmount {
		updater.GuaranteeAmount = *input.GuaranteeAmount
	}

	if input.ProductID != nil && *input.ProductID > 0 && *input.ProductID != lotInfo.ProductID {
		updater.ProductID = *input.ProductID
	}

	if input.ProductCode != nil && len(*input.ProductCode) > 0 && *input.ProductCode != lotInfo.ProductCode {
		updater.ProductCode = *input.ProductCode
	}

	isChangeAmount := false
	if input.Quantity != nil && *input.Quantity > 0 && *input.Quantity != lotInfo.Quantity {
		updater.Quantity = *input.Quantity
		updater.LotPrice = lotInfo.LotPrice
		updater.LotAmount = updater.Quantity * updater.LotPrice
		isChangeAmount = true
	}

	if input.LotPrice != nil && *input.LotPrice > 0 && *input.LotPrice != lotInfo.LotPrice {
		updater.LotPrice = *input.LotPrice
		if updater.Quantity == 0 {
			updater.Quantity = lotInfo.Quantity
		}
		updater.LotAmount = updater.Quantity * updater.LotPrice
		isChangeAmount = true
	}

	if input.LotName != nil && len(*input.LotName) > 0 && *input.LotName != lotInfo.LotName {
		updater.LotName = *input.LotName
	}

	if input.Note != nil && len(*input.Note) > 0 {
		updater.Note = *input.Note
	}

	if input.Unit != nil && len(*input.Unit) > 0 && *input.Unit != lotInfo.Unit {
		updater.Unit = *input.Unit
	}

	if input.RegistrationNo != nil && len(*input.RegistrationNo) > 0 && *input.RegistrationNo != lotInfo.RegistrationNo {
		updater.RegistrationNo = *input.RegistrationNo
	}

	if input.ManufacturerName != nil {
		updater.ManufacturerName = *input.ManufacturerName
	}

	if input.VAT != nil {
		updater.VAT = input.VAT
	}

	if input.OriginName != nil {
		updater.OriginName = *input.OriginName
	}

	lotUpdateReturnResp := model.LotDB.UpdateOne(model.Lot{
		LotID: lotInfo.LotID,
	}, updater)
	if !lotUpdateReturnResp.Ok() {
		return lotUpdateReturnResp
	}

	if isTriggerCount {
		lotWinResult := model.LotDB.Query(&model.Lot{Status: model.LotStatus.WIN, BidID: lotInfo.BidID}, 0, 1000, nil)
		if lotWinResult.Ok() {
			qty := int64(0)
			price := int64(0)
			for _, item := range lotWinResult.Data {
				qty = qty + 1
				price = price + item.LotAmount
			}
			_ = model.BidDB.UpdateOne(&model.Bid{BidID: lotInfo.BidID}, &model.Bid{
				WinningQuantity: &qty,
				WinningPrice:    &price,
			})
		}
	}

	if isChangeAmount {
		lotResult := model.LotDB.Query(&model.Lot{BidID: lotInfo.BidID}, 0, 1000, nil)
		if lotResult.Ok() {
			qty := int64(0)
			price := int64(0)
			for _, item := range lotResult.Data {
				qty = qty + 1
				price = price + item.LotAmount
			}

			_ = model.BidDB.UpdateOne(&model.Bid{BidID: lotInfo.BidID}, &model.Bid{
				NumOfProduct: qty,
				BidPrice:     price,
			})
		}
	}
	return lotUpdateReturnResp
}

// CreateLot ...
func CreateLot(input *request.CreateLotRequest, actionSource *request.ActionSource) common.Response {
	if checkResponse := input.Validate(); !checkResponse.Ok() {
		return checkResponse
	}

	// lotRes := model.LotDB.QueryOne(&model.Lot{
	// 	LotName:   input.LotName,
	// 	BidID:     input.BidID,
	// 	LotLineID: input.LotLineID,
	// })

	// if lotRes.Ok() {
	// 	return utils.NotfoundDataResponse[any]("Lot name existed", "LOT_NAME_EXISTED")
	// }

	// 2 cases:
	// a. product not listing
	// b. product existed

	lotRes := model.LotDB.QueryOne(&model.Lot{
		LotLineID: input.LotLineID,
		BidID:     input.BidID,
	})

	if lotRes.Ok() {
		return utils.NotfoundDataResponse[any]("Lot existed", "LOT_EXISTED")
	}

	bidResult := model.BidDB.QueryOne(&model.Bid{BidID: input.BidID})
	if !bidResult.Ok() {
		return utils.NotfoundDataResponse[any]("Bid not found", "BID_NOT_FOUND")
	}

	bidInfo := bidResult.Data[0]

	lotInfo := &model.Lot{
		BidID:            input.BidID,
		LotName:          input.LotName,
		LotPrice:         input.LotPrice,
		LotPriceUnit:     input.LotPriceUnit,
		LotAmount:        input.LotAmount,
		Quantity:         input.Quantity,
		GuaranteeAmount:  input.GuaranteeAmount,
		GroupMedicine:    input.GroupMedicine,
		Unit:             input.Unit,
		RegistrationNo:   input.RegistrationNo,
		Volume:           input.LotName,
		VAT:              input.VAT,
		OrgID:            bidInfo.OrgID,
		EntityID:         bidInfo.EntityID,
		LotLineID:        input.LotLineID,
		ManufacturerName: input.ManufacturerName,
		OriginName:       input.OriginName,
	}

	if input.ProductID > 0 {
		lotInfo.ProductID = input.ProductID
	}

	if input.ProductCode != "" {
		lotInfo.ProductCode = input.ProductCode
	}

	lotInfo.HashTag = strings.Replace(utils.NormalizeString(lotInfo.LotName), " ", "-", -1)

	if actionSource != nil && actionSource.Account != nil {
		lotInfo.CreatedByID = actionSource.Account.AccountID
		lotInfo.CreatedByName = actionSource.Account.FullName
	}

	lotInfo.LotID = model.GenLotID()
	lotInfo.Status = model.LotStatus.WAITING

	lotResp := model.LotDB.Create(lotInfo)
	if !lotResp.Ok() {
		return lotResp
	}

	// update increase
	lotRespReturn := lotResp.Data[0]
	if lotRespReturn.Status == model.LotStatus.WAITING {
		_ = model.BidDB.UpdateOneWithOperator(&model.Bid{BidID: bidInfo.BidID}, bson.M{
			"$inc": bson.M{"num_of_product": 1, "bid_price": lotInfo.LotAmount},
		})
	}

	return lotResp
}
