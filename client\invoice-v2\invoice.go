package invoice_v2

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout = 6 * time.Second
	pathCheckTax   = "/billing/invoice/v2/tax-code"
)

type client struct {
	host          string
	defaultClient *sdk_client.RestClient
	headers       map[string]string
}

var invoiceV2client *client

// InitInvoiceV2client ...
//
//	 Initialize invoiceV2client
//
//	 Parameters:
//		db: mongo database
func InitInvoiceV2client(db *mongo.Database) {
	invoiceV2client = &client{
		host: conf.Config.BuymedPlatformClient.Host,
		headers: map[string]string{
			"Authorization": conf.Config.BuymedPlatformClient.Authorization,
		},
		defaultClient: sdk_client.NewRESTClient(conf.Config.BuymedPlatformClient.Host, "invoice_v2_client", defaultTimeout, 0, 0),
	}
	invoiceV2client.defaultClient.SetDBLog(db)
	invoiceV2client.defaultClient.SetLoggerName("invoice_v2_client")
}

// CheckTax ...
//
//	 Check buyer tax code
//
//	 Parameters:
//		input: job.OrderOms
//
//	 Returns:
//		*common.APIResponse[request.BuyerTaxGOV]: response data
func CheckTax(input request.BuyerTaxGOV) *common.APIResponse[*request.BuyerTaxGOV] {
	resp, err := invoiceV2client.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, invoiceV2client.headers, nil, input, pathCheckTax)
	if err != nil {
		return &common.APIResponse[*request.BuyerTaxGOV]{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	var data *common.APIResponse[*request.BuyerTaxGOV]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return &common.APIResponse[*request.BuyerTaxGOV]{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return data
}
