package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

func CreateBeneficiary(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateBeneficiaryRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.CreateBeneficiary(&input, actionSource))
}

// QueryBeneficiaryList ...
func QueryBeneficiaryList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.BeneficiaryQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.QueryBeneficiaryList(&input.Q, input.Search, input.Offset, input.Limit, input.Option, actionSource))
}
