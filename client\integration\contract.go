package integration

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout                   = 30 * time.Second
	pathCreateContractDocument       = "/storage/document/v1/integration/contract/traditional/create-many"
	pathConfirmContractDocument      = "/storage/document/v1/integration/document/reference"
	pathCreateContractAccounting     = "/storage/document/v1/document-data"
	pathGetContractAccountingBalance = "/accounting/debt/v1/debt"
	pathCreateContractTransaction    = "/accounting/debt-adapter/v1/tender/event-order"
)

type client struct {
	host          string
	defaultClient *sdk_client.RestClient
	headers       map[string]string
}

type Department struct {
	Branch string `json:"branch"`
	Code   string `json:"code"`
}

// CreateContractDocumentRequest ...
type CreateContractDocumentRequest struct {
	OrgID           int64    `json:"orgID"`
	Name            string   `json:"name"`
	Description     string   `json:"description"`
	Tags            []string `json:"tags"`
	SearchText      string   `json:"searchText"`
	LegalEntityCode string   `json:"legalEntityCode"`
	SharedTarget    struct {
		SharedType string `json:"sharedType"`
		Viewer     struct {
			Users       []int64       `json:"users"`
			Departments []*Department `json:"departments"`
		} `json:"viewer"`
	} `json:"sharedTarget"`
	ExtendDocuments []struct {
		Name string `json:"name"`
		URL  string `json:"url"`
	} `json:"extendDocuments"`
	Documents []struct {
		URL string `json:"url"`
	} `json:"documents"`
	CustomerInfo struct {
		UserFullName    string `json:"userFullName"`
		IdentityType    string `json:"identityType"` // TAX
		IdentityNumber  string `json:"identityNumber"`
		UserPhoneNumber string `json:"userPhoneNumber"`
	} `json:"customerInfo"`
	Refs []map[string]string `json:"refs"`
}

// CreateContractDocumentResponse ...
type CreateContractDocumentResponse struct {
	Data  []*TraditionalContract `json:"data"`
	Total int64                  `json:"total"`
}

type CreateContractAccountingResponse struct {
	Code string `json:"code"`
}

// TraditionalContract ...
type TraditionalContract struct {
	Code string `json:"code"`
}

var contractClient *client

// InitContractClient ...
func InitContractClient(db *mongo.Database) {
	contractClient = &client{
		host: conf.Config.BuymedPlatformClient.Host,
		headers: map[string]string{
			"Authorization": conf.Config.BuymedPlatformClient.Authorization,
		},
		defaultClient: sdk_client.NewRESTClient(conf.Config.BuymedPlatformClient.Host, "integration_client", defaultTimeout, 0, 0),
	}

	contractClient.defaultClient.SetDBLog(db)
}

// CreateContractDocument ...
func CreateContractDocument(payload *CreateContractDocumentRequest) (string, error) {
	resp, err := contractClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, contractClient.headers, nil, payload, pathCreateContractDocument)
	if err != nil {
		return "", err
	}

	if resp.Code != http.StatusOK {
		return "", errors.New("error: fail to call create document contract")
	}

	var data *CreateContractDocumentResponse
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return "", errors.New("error: fail to read data traditional contract")
	}

	if len(data.Data) == 0 {
		return "", errors.New("error: traditional contract empty")
	}
	return data.Data[0].Code, nil
}

// CreateContractAccounting ...
func CreateContractAccounting(payload *job.CreateContractAccountingRequest) (string, error) {
	resp, err := contractClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, contractClient.headers, nil, payload, pathCreateContractAccounting)
	if err != nil {
		return "", err
	}

	if resp.Code != http.StatusOK {
		return "", errors.New("error: fail to call create document contract")
	}

	fmt.Println(resp.Body)

	var data *common.APIResponse[*TraditionalContract]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return "", errors.New("error: fail to read data contract")
	}

	if len(data.Data) == 0 || len(data.Data[0].Code) == 0 {
		return "", errors.New("error: fail to read data contract")
	}
	return data.Data[0].Code, nil
}

/*
curl --location --request QUERY 'https://api.stg.buymed.tech/accounting/debt/v1/debt' \
--header 'Authorization: Basic <>' \
--header 'Content-Type: application/json' \

	--data '{
	   "q": {
	      "documentDataCode": "NQTI6D1UPR"
	   }
	}'
*/
func GetContractAccountingBalance(documentDataCode string) (*request.ContractAccountingBalanceResponse, error) {
	payload := map[string]interface{}{
		"q": map[string]string{
			"documentDataCode": documentDataCode,
		},
	}
	resp, err := contractClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Query, contractClient.headers, nil, payload, pathGetContractAccountingBalance)
	if err != nil {
		return nil, err
	}

	fmt.Printf("%v\n", resp.Body)
	if resp.Code != http.StatusOK {
		return nil, errors.New("error: fail to call order gen id")
	}

	var data *common.APIResponse[*request.ContractAccountingBalanceResponse]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return nil, errors.New("error: fail to read data because " + err.Error())
	}

	if len(data.Data) == 0 {
		return nil, errors.New("error: fail to read data empty ")
	}

	return data.Data[0], nil
}

func CreateContractTransaction(payload *CreateContractTransactionRequest) (interface{}, error) {
	resp, err := contractClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, contractClient.headers, nil, payload, pathCreateContractTransaction)
	if err != nil {
		return "", err
	}

	if resp.Code != http.StatusOK {
		return "", errors.New("error: fail to call create document contract")
	}

	fmt.Println(resp.Body)

	var data *common.APIResponse[any]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return "", errors.New("error: fail to read data contract")
	}

	return nil, nil
}
