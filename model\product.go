// Package product
package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Product ...
type Product struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code           string `json:"code,omitempty" bson:"code,omitempty"`
	CreatedByName  string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID    int64  `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName  string `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID    int64  `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID          int64  `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID       int64  `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	ContractID     int64  `json:"contractID,omitempty" bson:"contract_id,omitempty"`
	ContractCode   string `json:"contractCode,omitempty" bson:"contract_code,omitempty"`
	ContractType   string `json:"contractType,omitempty" bson:"contract_type,omitempty"`
	MainContractID int64  `json:"mainContractID,omitempty" bson:"main_contract_id,omitempty"`
	LotID          int64  `json:"lotID,omitempty" bson:"lot_id,omitempty"`
	BidID          int64  `json:"bidID,omitempty" bson:"bid_id,omitempty"`

	Sku          string `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductID    int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode  string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductName  string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductMapID int64  `json:"productMapID,omitempty" bson:"product_map_id,omitempty"`
	Price        int64  `json:"price,omitempty" bson:"price,omitempty"`
	Currency     string `json:"currency,omitempty" bson:"currency,omitempty"` // VND
	Amount       int64  `json:"amount,omitempty" bson:"amount,omitempty"`

	QuantityInitial    int64  `json:"quantityInitial,omitempty" bson:"quantity_initial,omitempty"` // so luong ban dau
	Quantity           int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`                // so luong tong
	QuantityProcessing *int64 `json:"quantityProcessing,omitempty" bson:"quantity_processing,omitempty"`
	QuantitySold       *int64 `json:"quantitySold,omitempty" bson:"quantity_sold,omitempty"`     // so luong da ban
	QuantityRemain     int64  `json:"quantityRemain,omitempty" bson:"quantity_remain,omitempty"` // so luong con lai
	GuaranteeAmount    int64  `json:"guaranteeAmount,omitempty" bson:"guarantee_amount,omitempty"`
	GroupMedicine      int64  `json:"groupMedicine,omitempty" bson:"group_medicine,omitempty"`
	Unit               string `json:"unit,omitempty" bson:"unit,omitempty"`
	RegistrationNo     string `json:"registrationNo,omitempty" bson:"registration_no,omitempty"`
	VAT                *int   `json:"vat,omitempty" bson:"vat,omitempty"`

	ManufacturerName string `json:"manufacturerName,omitempty" bson:"manufacturer_name,omitempty"`
	OriginName       string `json:"originName,omitempty" bson:"origin_name,omitempty"`
	Volume           string `json:"volume,omitempty" bson:"volume,omitempty"`

	// ForQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	Contract     *Contract `json:"contract,omitempty" bson:"-"`
	IsRemove     bool      `json:"-" bson:"-"`

	Scope []string `json:"scope,omitempty" bson:"scope,omitempty"`
}

// ProductStatusValue ...
type ProductStatusValue string

// ProductStatusEnt ...
type ProductStatusEnt struct {
	DRAFT ProductStatusValue
}

// ProductStatus ...
var ProductStatus = &ProductStatusEnt{
	"DRAFT",
}

// ProductDB ...
var ProductDB = &mongodb.Instance[*Product]{
	ColName: "products",
}

// InitProductModel ....
func InitProductModel(s *mongo.Database) {
	ProductDB.ApplyDatabase(s)

	ProductDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	ProductDB.CreateIndex(bson.D{
		{Key: "status", Value: 1},
	}, &options.IndexOptions{})
}

// IsValidProductStatus ...
func IsValidProductStatus(status string) bool {
	switch ProductStatusValue(status) {
	case ProductStatus.DRAFT:
		{
			return true
		}
	}
	return false
}
