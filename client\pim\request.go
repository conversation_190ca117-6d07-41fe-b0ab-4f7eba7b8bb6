package pim

type ConversionUnitRequest struct {
	CountryCode string         `json:"countryCode"`
	Mode        string         `json:"mode"`
	Products    []*ProductUnit `json:"products"`
}

type ProductUnit struct {
	ProductID    int64  `json:"productID"`
	FromUnit     string `json:"fromUnit"`
	FromQuantity int64  `json:"fromQuantity"`
	ToUnit       string `json:"toUnit"`
}

type ProductUnitResponse struct {
	ProductID         int64   `json:"productID" bson:"product_id,omitempty"`
	FromUnit          string  `json:"fromUnit" bson:"from_unit,omitempty"`
	FromUnitName      string  `json:"fromUnitName" bson:"from_unit_name,omitempty"`
	FromQuantity      int64   `json:"fromQuantity" bson:"from_quantity,omitempty"`
	ToUnit            string  `json:"toUnit" bson:"to_unit,omitempty"`
	ToUnitName        string  `json:"toUnitName" bson:"to_unit_name,omitempty"`
	ToQuantity        int64   `json:"toQuantity" bson:"to_quantity,omitempty"`
	IsValid           bool    `json:"isValid" bson:"-"`
	RemainderQuantity int64   `json:"remainderQuantity,omitempty" bson:"-"`
	RemainderUnit     string  `json:"remainderUnit,omitempty" bson:"-"`
	Rate              []int64 `json:"rate" bson:"rate,omitempty"`
	Description       string  `json:"description,omitempty" bson:"-"`
}

type ConversionUnitResponse struct {
	Mode     string                 `json:"mode"`
	Products []*ProductUnitResponse `json:"products"`
}

type getConversionUnitRequest struct {
	CountryCode string  `json:"countryCode"`
	ProductIDs  []int64 `json:"productIDs"`
	Value       int64   `json:"value"`
}

type queryGetConversionUnitRequest struct {
	Q *getConversionUnitRequest `json:"q"`
}

type queryGetConversionUnitResponse struct {
	Status  string            `json:"status"`
	Message string            `json:"message"`
	Data    []*ConversionUnit `json:"data"`
}

type ConversionUnit struct {
	BaseUnit     string `json:"baseUnit"`
	BaseUnitName string `json:"baseUnitName"`
	ProductID    int64  `json:"productID"`
	Unit         string `json:"unit"`
	UnitName     string `json:"unitName"`
	Value        int64  `json:"value"`
}

type baseResponse[T any] struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Data    []T    `json:"data"`
}
