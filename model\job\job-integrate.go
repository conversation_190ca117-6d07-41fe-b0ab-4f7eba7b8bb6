package job

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue/basic_queue"
	"go.mongodb.org/mongo-driver/mongo"
)

type CreateContractAccountingRequest struct {
	Name         string                        `json:"name"`
	TemplateCode string                        `json:"templateCode"`
	Status       string                        `json:"status"`
	Data         DataContractAccountingRequest `json:"data"`
}

type DataContractAccountingRequest struct {
	ContractID       int64    `json:"contractId"` // main key
	ContractNo       string   `json:"contractNo"`
	ContractFile     []string `json:"contractFile"`
	ContractAddendum []string `json:"contractAddendum"` // phu luc

	PartyA       string `json:"partyA"`
	PartyACode   string `json:"partyCodeA"`
	PartyAType   string `json:"partyTypeA"`
	TaxCodeA     string `json:"taxCodeA"`
	AddressA     string `json:"addressA"`
	BankAccountA string `json:"bankAccountA"`
	BankNameA    string `json:"bankNameA"`
	EmailA       string `json:"emailA"`
	FaxA         string `json:"faxA"`
	PhoneNumberA string `json:"phoneNumberA"`

	PartyB       string `json:"partyB"`
	PartyBType   string `json:"partyTypeB"`
	TaxCodeB     string `json:"taxCodeB"`
	AddressB     string `json:"addressB"`
	EmailB       string `json:"emailB"`
	PhoneNumberB string `json:"phoneNumberB"`

	DebtLimit   int64      `json:"debtLimit"`
	PaymentTerm int64      `json:"paymentTerm"`
	StartDate   *time.Time `json:"startDate"`
	EndDate     *time.Time `json:"endDate"`
	PartyBCode  string     `json:"partyCodeB"`
}

var JobSyncContractToAccounting *basic_queue.Queue[*CreateContractAccountingRequest]

func InitJobSyncContractToAccounting(session *mongo.Database) {

	JobSyncContractToAccounting = basic_queue.New[*CreateContractAccountingRequest](
		"tender_sync_contract_to_accounting", &db_queue.Configuration{
			CurVersionTimeoutS:  20 * 60, // 20 min
			OldVersionTimeoutS:  10 * 60, // 10 min
			LogSize:             5,
			MaximumWaitToRetryS: 3, // 3 secs
			ChannelCount:        4,
			ConsumedExpiredTime: time.Duration(7*24) * time.Hour,
			FailThreshold:       3,
			UniqueItem:          true,
		},
	)

	JobSyncContractToAccounting.Init(session)
}
