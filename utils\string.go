package utils

import "strings"

func UniqueStringSlice(str []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range str {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// IsContains checks if a string slice contains a specific key.
func IsContains(arr []string, key string) bool {
	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if key == arr[i] {
			return true
		}
	}

	return false
}

// NormalizeTaxCode removes any whitespace and normalizes the tax code format
func NormalizeTaxCode(taxCode string) string {
	// Remove all whitespace
	return strings.ReplaceAll(taxCode, " ", "")
}
