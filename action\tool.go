package action

import (
	"fmt"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/enum"
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
)

// UpdateContract ...
func ToolUpdateContractTaxCode(input *request.UpdateContractTaxCodeRequest, actionSource *request.ActionSource) common.Response {
	if input.Code == "" {
		return utils.InvalidResponse("Invalid Contract Code", "INVALID_CONTRACT_CODE")
	}

	if input.TaxCode == nil || *input.TaxCode == "" {
		return utils.InvalidResponse("Invalid Contract Tax Code", "INVALID_CONTRACT_TAX_CODE")
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: input.Code,
	})

	if !contractResp.Ok() {
		return utils.NotfoundDataResponse[any]("Not found any matched contract", "CONTRACT_NOT_FOUND")
	}

	// trim and replace all space, tab, new line
	*input.TaxCode = strings.TrimSpace(*input.TaxCode)
	*input.TaxCode = strings.Replace(*input.TaxCode, " ", "", -1)
	*input.TaxCode = strings.Replace(*input.TaxCode, `\t`, ``, -1)
	*input.TaxCode = strings.Replace(*input.TaxCode, `\n`, ``, -1)

	contractInfo := contractResp.Data[0]
	contractInfo.TaxCode = *input.TaxCode

	// update tax code
	resp := model.ContractDB.UpdateOne(model.Contract{
		ContractID: contractInfo.ContractID,
	}, contractInfo)

	if !resp.Ok() {
		return resp
	}
	newContractInfo := resp.Data[0]

	// update beneficiary
	beneficiaryResp := model.BeneficiaryDB.QueryOne(&model.Beneficiary{
		BeneficiaryID: contractInfo.BeneficiaryID,
		Code:          contractInfo.BeneficiaryCode,
	})

	if beneficiaryResp.Ok() {
		beneficiaryInfo := beneficiaryResp.Data[0]
		beneficiaryInfo.TaxCode = newContractInfo.TaxCode
		model.BeneficiaryDB.UpdateOne(model.Beneficiary{
			BeneficiaryID: beneficiaryInfo.BeneficiaryID,
		}, beneficiaryInfo)
	}

	// trigger make contract accounting
	if contractInfo.Status == model.ContractStatus.ACTIVE &&
		len(conf.Config.TemplateCode) > 0 &&
		contractInfo.DebtLimit > 0 {
		newMsg := &job.CreateContractAccountingRequest{
			Name:         contractInfo.Name,
			TemplateCode: conf.Config.TemplateCode,
			Status:       "ACTIVE",
			Data: job.DataContractAccountingRequest{
				ContractID:       contractInfo.ContractID,
				ContractNo:       contractInfo.ContractNumber,
				ContractFile:     contractInfo.Attachments,
				ContractAddendum: contractInfo.ExtendAttachments,
				PartyA:           contractInfo.LegalEntityName,
				PartyAType:       "COMPANY",
				PartyACode:       contractInfo.LegalEntityCode,
				TaxCodeA:         contractInfo.LegalEntityTaxCode,
				AddressA:         contractInfo.LegalEntityAddress,
				BankAccountA:     contractInfo.LegalEntityBankAccount,
				BankNameA:        contractInfo.LegalEntityBankName,
				EmailA:           contractInfo.LegalEntityEmail,
				PhoneNumberA:     contractInfo.LegalEntityTel,
				PartyB:           contractInfo.BeneficiaryName,
				PartyBType:       "TENDERVN_CUSTOMER",
				TaxCodeB:         newContractInfo.TaxCode,
				AddressB:         contractInfo.Address,
				PhoneNumberB:     contractInfo.PhoneNumber,
				EmailB:           contractInfo.Email,
				DebtLimit:        contractInfo.DebtLimit,
				PaymentTerm:      contractInfo.PaymentTerm,
				StartDate:        contractInfo.SigningDate,
				EndDate:          contractInfo.ExpireDate,
				PartyBCode:       contractInfo.BeneficiaryCode,
			},
		}
		job.JobSyncContractToAccounting.Push(newMsg, &db_queue.ItemMetadata{
			Topic:     "default",
			ReadyTime: utils.AnyToPointer(time.Now().Add(30 * time.Second)),
			UniqueKey: fmt.Sprint(contractInfo.ContractID),
			SortedKey: fmt.Sprint(contractInfo.ContractID),
			Keys: []string{
				fmt.Sprint(contractInfo.ContractID),
			},
		})
	}

	return resp
}

// Update order tax code ...
func ToolUpdateOrderTaxCode(input *request.UpdateOrderTaxCodeRequest, actionSource *request.ActionSource) common.Response {
	if input.OrderCode == "" {
		return utils.InvalidResponse("Invalid Order Code", "INVALID_ORDER_CODE")
	}

	if input.TaxCode == nil || *input.TaxCode == "" {
		return utils.InvalidResponse("Invalid Order Tax Code", "INVALID_ORDER_TAX_CODE")
	}

	// query order
	orderResp := model.OrderDB.QueryOne(&model.Order{
		OrderCode: input.OrderCode,
	})

	if !orderResp.Ok() {
		return orderResp
	}

	order := orderResp.Data[0]

	// // query contract
	// contractResult := model.ContractDB.QueryOne(&model.Contract{
	// 	Code: order.ContractCode,
	// })
	// if !contractResult.Ok() {
	// 	return utils.InvalidResponse("Order Contract Not Found", "ORDER_CONTRACT_NOT_FOUND")
	// }

	// contract := contractResult.Data[0]
	// // query beneficiary
	// beneficiaryResp := model.BeneficiaryDB.QueryOne(&model.Beneficiary{
	// 	BeneficiaryID: contract.BeneficiaryID,
	// 	Code:          contract.BeneficiaryCode,
	// })

	// if !beneficiaryResp.Ok() {
	// 	return beneficiaryResp
	// }
	// beneficiary := beneficiaryResp.Data[0]

	// i want only update tax code

	// model.BeneficiaryDB.UpdateOne(model.Beneficiary{
	// 	BeneficiaryID: beneficiary.BeneficiaryID,
	// 	Code:          beneficiary.Code,
	// }, model.Beneficiary{
	// 	TaxCode: contract.TaxCode,
	// })

	order.Invoice.TaxCode = *input.TaxCode

	// update order
	updateResp := model.OrderDB.UpdateOne(model.Order{
		OrderID: order.OrderID,
	}, order)

	return updateResp
}

// Tool sync order invoice
func ToolSyncOrderInvoice(input *request.SyncOrderInvoiceRequest, actionSource *request.ActionSource) common.Response {
	if input.OrderCode == "" {
		return utils.InvalidResponse("Invalid Order Code", "INVALID_ORDER_CODE")
	}

	// query order
	orderResp := model.OrderDB.QueryOne(&model.Order{
		OrderCode: input.OrderCode,
	})

	if !orderResp.Ok() {
		return orderResp
	}

	order := orderResp.Data[0]

	// query contract
	contractResult := model.ContractDB.QueryOne(&model.Contract{
		Code: order.ContractCode,
	})
	if !contractResult.Ok() {
		return utils.InvalidResponse("Order Contract Not Found", "ORDER_CONTRACT_NOT_FOUND")
	}

	contract := contractResult.Data[0]

	// update invoice info
	order.Invoice.CompanyName = contract.BeneficiaryName
	order.Invoice.TaxCode = contract.TaxCode
	order.Invoice.CompanyAddress = contract.Address
	order.Invoice.Email = contract.Email

	// update order
	updateResp := model.OrderDB.UpdateOne(model.Order{
		OrderID: order.OrderID,
	}, order)

	return updateResp
}

// force cancel order
func ToolForceCancelOrder(input *request.ForceCancelOrderRequest, actionSource *request.ActionSource) common.Response {
	if input.OrderCode == "" {
		return utils.InvalidResponse("Invalid Order Code", "INVALID_ORDER_CODE")
	}

	// query order
	orderResp := model.OrderDB.QueryOne(&model.Order{
		OrderCode: input.OrderCode,
	})

	if !orderResp.Ok() {
		return orderResp
	}

	order := orderResp.Data[0]

	updateRs := model.OrderDB.UpdateOne(&model.Order{
		OrderID: order.OrderID,
		Status:  order.Status,
	}, &model.Order{
		Status:        string(enum.OrderState.DELETING),
		UpdatedByName: "",
		UpdatedByID:   0,
	})
	if !updateRs.Ok() {
		return utils.ErrorResponse(updateRs.Message, updateRs.ErrorCode)
	}

	es := revertQuantityProduct(order)
	if !es.Ok() {
		return utils.ErrorResponse(es.Message, es.ErrorCode)
	}

	updateRs = model.OrderDB.UpdateOne(&model.Order{
		OrderID: order.OrderID,
		Status:  string(enum.OrderState.DELETING),
	}, &model.Order{
		Status:        string(enum.OrderState.CANCELLED),
		UpdatedByName: "",
		UpdatedByID:   0,
	})
	return updateRs
}
