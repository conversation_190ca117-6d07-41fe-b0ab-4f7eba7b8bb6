package request

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
)

type OrderBidQuery struct {
	OrderID             int64     `json:"orderID,omitempty" bson:"order_id,omitempty"`
	BidID               int64     `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	ContractCode        string    `json:"contractCode,omitempty" bson:"contract_code,omitempty"`
	ReceiverCode        string    `json:"receiverCode,omitempty" bson:"receiver_code,omitempty"`
	ReceiverPhoneNumber string    `json:"receiverPhoneNumber,omitempty" bson:"receiver_phone_number,omitempty"`
	Status              string    `json:"status,omitempty" bson:"status,omitempty"`
	ComplexQuery        []*bson.M `json:"-" bson:"$and,omitempty"`
}

type OrderQuery struct {
	OrderID      int64     `json:"orderID,omitempty"`
	Status       string    `json:"status,omitempty"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type GetOrderInfoQuery struct {
	Contract bool
	Bid      bool
}

type CreateOrderRequest struct {
	ContractCode            string                    `json:"contractCode"`
	RegionCode              string                    `json:"regionCode"`
	ProvinceCode            string                    `json:"provinceCode"`
	DistrictCode            string                    `json:"districtCode"`
	WardCode                string                    `json:"wardCode"`
	ReceiverShippingAddress string                    `json:"shippingAddress"`
	ReceiverPhoneNumber     string                    `json:"phoneNumber"`
	ReceiverName            string                    `json:"receiverName"`
	Note                    string                    `json:"note"`
	CanExportInvoice        bool                      `json:"canExportInvoice"`
	Items                   []*CreateOrderItemRequest `json:"products"`
	BudgetUnitCode          string                    `json:"budgetUnitCode"`
}

type CreateOrderItemRequest struct {
	ContractProductCode string `json:"contractProductCode"`
	Quantity            int64  `json:"quantity"`
}

func (m *CreateOrderRequest) Validate() *common.APIResponse[any] {
	if len(m.ContractCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "ContractCode required",
			ErrorCode: "INVALID_CONTRACT_CODE",
		}
	}

	if len(m.ReceiverShippingAddress) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "ShippingAddress required",
			ErrorCode: "INVALID_SHIPPING_ADDRESS",
		}
	}

	if len(m.ReceiverPhoneNumber) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "PhoneNumber required",
			ErrorCode: "INVALID_PHONE_NUMBER",
		}
	}

	if len(m.RegionCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "RegionCode required",
			ErrorCode: "INVALID_REGION_CODE",
		}
	}

	if len(m.ProvinceCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "ProvinceCode required",
			ErrorCode: "INVALID_PROVINCE_CODE",
		}
	}

	if len(m.DistrictCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "DistrictCode required",
			ErrorCode: "INVALID_DISTRICT_CODE",
		}
	}

	if len(m.Items) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Items required",
			ErrorCode: "INVALID_ITEMS",
		}
	}

	for idx, item := range m.Items {
		if len(item.ContractProductCode) == 0 {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductCode[%d] required", idx),
				ErrorCode: "INVALID_PRODUCT_CODE",
			}
		}
		if item.Quantity <= 0 {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Quantity[%d] required", idx),
				ErrorCode: "INVALID_QUANTITY",
			}
		}
	}

	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}

type UpdateOrderStatusRequest struct {
	OrderID int64  `json:"orderID"`
	Status  string `json:"status"`
}

func (m *UpdateOrderStatusRequest) Validate() *common.APIResponse[any] {
	if m.OrderID <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid order id",
			ErrorCode: "INVALID_ORDER_ID",
		}
	}

	if len(m.Status) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid order status",
			ErrorCode: "INVALID_ORDER_STATUS",
		}
	}

	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}

type UpdateSaleOrderFulfillmentRequest struct {
	OrderID              int64      `json:"orderID" bson:"order_id"`
	SaleOrderCode        *string    `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	SaleOrderCreatedTime *time.Time `json:"saleOrderCreatedTime,omitempty" bson:"sale_order_created_time,omitempty"` // thời gian tạo đơn hàng warehouse
	Status               *string    `json:"status,omitempty" bson:"status,omitempty"`                                // trạng thái đơn hàng
	WarehouseCode        *string    `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	InvoiceNo            *string    `json:"invoiceNo,omitempty" bson:"invoice_no,omitempty"`
	InvoiceExchangeNo    *string    `json:"invoiceExchangeNo,omitempty" bson:"invoice_exchange_no,omitempty"`
	ActualTotalPrice     *float64   `json:"actualTotalPrice,omitempty" bson:"actual_total_price,omitempty"` // tổng tiền cuối cùngs
	ActualPrice          *float64   `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`            // tổng tiền hàng thực tế

	CompletedTime     *time.Time       `json:"completedTime,omitempty" bson:"completed_time,omitempty"`          // thời gian hoàn tất đơn hàng -- status = completed
	CompletedDebtTime *time.Time       `json:"completedDebtTime,omitempty" bson:"completed_debt_time,omitempty"` // thời gian hoàn tất công nợ
	OutboundDate      *time.Time       `json:"outboundDate,omitempty" bson:"outbound_date,omitempty"`            // ngày xuất kho
	ProcessStartTime  *time.Time       `json:"processStartTime,omitempty" bson:"process_start_time,omitempty"`   // ngày bắt đầu xử lý
	DeliveredTime     *time.Time       `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`          // ngày giao hàng
	CancelTime        *time.Time       `json:"cancelTime,omitempty" bson:"cancel_time,omitempty"`                // ngày huỷ
	PaidTime          *time.Time       `json:"paidTime,omitempty" bson:"paid_time,omitempty"`                    // thời gian cuối cùng thanh toán
	PaidAmount        *int64           `json:"paidAmount,omitempty" bson:"paid_amount,omitempty"`                // so tien thanh toan
	PaymentStatus     *string          `json:"paymentStatus,omitempty" bson:"payment_status,omitempty"`          // trạng thái thanh toán của đơn hàng
	Items             []*UpdateItemMsg `json:"items,omitempty" bson:"items,omitempty"`
}

type UpdateItemMsg struct {
	Sku               string              `json:"sku,omitempty"`
	Quantity          *int64              `json:"quantity,omitempty"`
	Unit              string              `json:"unit,omitempty"`
	Volume            string              `json:"volume,omitempty"`
	OutboundQuantity  *int64              `json:"outboundQuantity,omitempty" bson:"outbound_quantity,omitempty"`
	DeliveredQuantity *int64              `json:"deliveredQuantity,omitempty" bson:"delivered_quantity,omitempty"`
	CompletedQuantity *int64              `json:"completedQuantity,omitempty" bson:"completed_quantity,omitempty"`
	OutboundInfos     []*ItemQuantityInfo `json:"outboundInfos,omitempty" bson:"outbound_infos,omitempty"`
	ReturnInfos       []*ItemQuantityInfo `json:"returnInfos,omitempty" bson:"return_infos,omitempty"`
}

type ItemQuantityInfo struct {
	Lot      string `json:"lot,omitempty" bson:"lot,omitempty"`
	ExpDate  string `json:"expDate,omitempty" bson:"exp_date,omitempty"`
	Quantity int    `json:"quantity,omitempty" bson:"quantity,omitempty"`
}
