package request

type ReviewProductUnitRequest struct {
	Products []*ReviewProductUnit `json:"products"`
}

type ReviewProductUnitResponse struct {
	Products []*ReviewProductUnit `json:"products"`
}

type ReviewProductUnit struct {
	ProductID         int64   `json:"productID"`
	FromUnit          string  `json:"fromUnit"`
	FromUnitName      string  `json:"fromUnitName"`
	FromQuantity      int64   `json:"fromQuantity"`
	ToUnit            string  `json:"toUnit"`
	ToUnitName        string  `json:"toUnitName"`
	ToQuantity        int64   `json:"toQuantity"`
	Price             int64   `json:"price"`
	RemainderQuantity int64   `json:"remainderQuantity,omitempty"`
	RemainderUnit     string  `json:"remainderUnit,omitempty"`
	Rate              []int64 `json:"rate,omitempty"`
}

type ProductUnitQuery struct {
	ProductIDs []int64 `json:"productIDs"`
}
