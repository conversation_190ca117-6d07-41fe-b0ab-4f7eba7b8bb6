// Package api ...
package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// CreateLot ...
func CreateLot(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateLotRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.CreateLot(&input, actionSource))
}

// GetLotInfo ...
func GetLotInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	var lotID = utils.ParseInt64(params["lotID"], 0)
	return resp.Respond(action.GetLotInfo(lotID))
}

// UpdateLot ...
func UpdateLot(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateLotRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.UpdateLotInfo(&input, actionSource))
}

// QueryLotList ...
func QueryLotList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.LotQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	return resp.Respond(action.QueryLotList(&input.Q, input.Search, input.Offset, input.Limit, input.Option))
}
