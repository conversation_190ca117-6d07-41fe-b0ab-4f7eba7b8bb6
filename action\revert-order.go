package action

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func revertQuantityProduct(order *model.Order) common.APIResponse[any] {
	orderItemResult := model.OrderItemDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 1000, nil)
	if !orderItemResult.Ok() {
		return common.APIResponse[any]{
			ErrorCode: orderItemResult.ErrorCode,
			Status:    orderItemResult.Status,
			Message:   orderItemResult.Message,
		}
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code:   order.ContractCode,
		Status: model.ContractStatus.ACTIVE,
	})
	if !contractResp.Ok() {
		return common.APIResponse[any]{
			ErrorCode: contractResp.ErrorCode,
			Status:    contractResp.Status,
			Message:   contractResp.Message,
		}
	}

	orderItems := orderItemResult.Data
	contract := contractResp.Data[0]
	for _, orderItem := range orderItems {
		_ = model.ProductDB.UpdateOneWithOperator(&model.Product{
			Code:           orderItem.ContractProductCode,
			MainContractID: contract.ContractID,
			ComplexQuery: []*primitive.M{
				{
					"quantity_processing": bson.M{
						"$gte": orderItem.Quantity,
					},
				},
			},
		}, bson.M{
			"$inc": bson.M{
				"quantity_remain":     orderItem.Quantity,
				"quantity_processing": -orderItem.Quantity,
			},
		})
	}

	return common.APIResponse[any]{
		Status:  contractResp.Status,
		Message: contractResp.Message,
	}
}
