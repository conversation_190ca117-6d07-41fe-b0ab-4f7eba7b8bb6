package utils

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
)

type AnyAPIResponse = common.APIResponse[any]

func UnauthorizedResponse() *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Unauthorized",
		ErrorCode: "UNAUTHORIZED",
	}
}

func UnauthorizedResponseWithError(msg, errCode string) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func InvalidResponse(msg, errCode string) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func ForbiddenResponse(msg, errCode string) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.Forbidden,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func ExistedResponse(msg, errCode string) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func ErrorResponse(msg, errCode string) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.Error,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func NotfoundResponse(msg, errCode string) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:    common.APIStatus.NotFound,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func NotfoundDataResponse[T any](msg, errCode string) *common.APIResponse[T] {
	return &common.APIResponse[T]{
		Status:    common.APIStatus.NotFound,
		Message:   msg,
		ErrorCode: errCode,
	}
}
func InvalidDataResponse[T any](msg, errCode string) *common.APIResponse[T] {
	return &common.APIResponse[T]{
		Status:    common.APIStatus.Invalid,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func ForbiddenDataResponse[T any](msg, errCode string) *common.APIResponse[T] {
	return &common.APIResponse[T]{
		Status:    common.APIStatus.Forbidden,
		Message:   msg,
		ErrorCode: errCode,
	}
}

func ExistedDataResponse[T any](msg, errCode string, data []T) *common.APIResponse[T] {
	return &common.APIResponse[T]{
		Status:    common.APIStatus.Invalid,
		Message:   msg,
		ErrorCode: errCode,
		Data:      data,
	}
}

func OkResponse(msg string, data []any) *common.APIResponse[any] {
	return &AnyAPIResponse{
		Status:  common.APIStatus.Ok,
		Message: msg,
		Data:    data,
	}
}

func InvalidParseJSONData(err error) *common.APIResponse[any] {
	return InvalidResponse("Can not parse input data. Detail: "+err.Error(), "INVALID_JSON_INPUT")
}

func ErrorDataResponse[T any](msg, errCode string) *common.APIResponse[T] {
	return &common.APIResponse[T]{
		Status:    common.APIStatus.Error,
		Message:   msg,
		ErrorCode: errCode,
	}
}

// ToAPIResponse ...
func ToAPIResponse[T any](c *client.RestResult) (*common.APIResponse[T], error) {
	var rs *common.APIResponse[T]
	err := json.Unmarshal(c.Content, &rs)
	if err != nil {
		return nil, err
	}
	return &common.APIResponse[T]{Status: rs.Status, Message: rs.Message, Data: rs.Data, ErrorCode: rs.ErrorCode}, nil
}
