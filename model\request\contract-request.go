package request

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
)

// ContractQuery ...
type ContractQuery struct {
	ContractCodes   []string   `json:"contractCodes,omitempty" bson:"-"`
	ContractIDs     []int64    `json:"contractIDs,omitempty" bson:"-"`
	InvitationOfBid string     `json:"invitationOfBid,omitempty" bson:"itb,omitempty"`
	ContractNumber  string     `json:"contractNumber,omitempty" bson:"contract_number,omitempty"`
	ContractType    string     `json:"-" bson:"contract_type,omitempty"`
	CreatedFrom     *time.Time `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo       *time.Time `json:"createdTo,omitempty" bson:"-"`
	Status          string     `json:"status,omitempty" bson:"status,omitempty"`
	BeneficiaryCode string     `json:"beneficiaryCode,omitempty" bson:"beneficiary_code,omitempty"`
	ComplexQuery    []*bson.M  `json:"-" bson:"$and,omitempty"`
}

// CreateContractRequest ...
type CreateContractRequest struct {
	Name              string            `json:"name"`
	InvitationOfBid   *string           `json:"invitationOfBid"`
	BidID             *int64            `json:"bidID"`
	ContractCode      string            `json:"contractCode"`
	ContractNumber    string            `json:"contractNumber"`
	ContractType      string            `json:"contractType"`
	DebtLimit         int64             `json:"debtLimit"`
	PaymentTerm       int64             `json:"paymentTerm"`
	BeneficiaryName   string            `json:"beneficiaryName"`
	DecisionNo        string            `json:"decisionNo"`
	TaxCode           string            `json:"taxCode"`
	PhoneNumber       string            `json:"phoneNumber"`
	Email             string            `json:"email"`
	BudgetUnitCode    string            `json:"budgetUnitCode"`
	TermOfPayment     int64             `json:"termOfPayment"`
	Address           string            `json:"address"`
	SigningDate       *time.Time        `json:"signingDate"`
	ExpireDate        *time.Time        `json:"expireDate"`
	Content           string            `json:"content"`
	Note              string            `json:"note"`
	Attachments       []string          `json:"attachments"`
	ExtendAttachments []string          `json:"extendAttachments"`
	Products          []*ProductRequest `json:"products"`

	// legal entity
	LegalEntityCode     string          `json:"legalEntityCode"`
	LegalEntityName     string          `json:"legalEntityName"`
	LegalEntityTaxCode  string          `json:"legalEntityTaxCode"`
	LegalEntityAddress  string          `json:"legalEntityAddress"`
	LegalEntityEmail    string          `json:"legalEntityEmail"`
	LegalEntityTel      string          `json:"legalEntityTel"`
	LegalEntityBankCode string          `json:"legalEntityBankCode"`
	LegalEntityBank     LegalEntityBank `json:"legalEntityBank"`
	LegalEntityBranch   string          `json:"legalEntityBranch"`
}

type LegalEntityBank struct {
	BankName      string `json:"bankName"`
	BankBranch    string `json:"bankBranch"`
	BankCode      string `json:"bankCode"`
	AccountNumber string `json:"accountNumber"`
}

func (m *CreateContractRequest) Validate() *common.APIResponse[any] {
	if m.BidID != nil && *m.BidID <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid BidID",
			ErrorCode: "INVALID_BID_ID",
		}
	}

	if len(m.Name) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Name required",
			ErrorCode: "INVALID_NAME",
		}
	}

	if len(m.ContractNumber) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "ContractNumber required",
			ErrorCode: "INVALID_CONTRACT_NO",
		}
	}

	if len(m.TaxCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "TaxCode required",
			ErrorCode: "INVALID_TAX",
		}
	}

	// validate legal entity
	if len(m.LegalEntityCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Legal code required",
			ErrorCode: "LEGAL_CODE_MISSING",
		}
	}

	if len(m.LegalEntityName) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Legal name required",
			ErrorCode: "LEGAL_NAME_MISSING",
		}
	}

	if len(m.LegalEntityTaxCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Legal tax code required",
			ErrorCode: "LEGAL_NAME_MISSING",
		}
	}

	if len(m.LegalEntityAddress) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Legal address required",
			ErrorCode: "LEGAL_ADDRESS_MISSING",
		}
	}

	if len(m.LegalEntityEmail) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Legal email required",
			ErrorCode: "LEGAL_EMAIL_MISSING",
		}
	}

	if len(m.LegalEntityBank.BankName) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Bank name required",
			ErrorCode: "BANK_NAME_MISSING",
		}
	}

	if len(m.LegalEntityBank.BankBranch) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Bank branch required",
			ErrorCode: "BANK_BRANCH_MISSING",
		}
	}

	if len(m.LegalEntityBank.BankCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Bank code required",
			ErrorCode: "BANK_CODE_MISSING",
		}
	}

	if len(m.LegalEntityBank.AccountNumber) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Bank account number required",
			ErrorCode: "BANK_ACCOUNT_MISSING",
		}
	}

	mapLots := make(map[int64]*ProductRequest)
	for idx, product := range m.Products {
		if mapLots[product.LotID] != nil {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Lot[%d] duplicate", idx),
				ErrorCode: "INVALID_LOT",
			}
		}
		mapLots[product.LotID] = product

		if m.BidID != nil {
			if product.LotID <= 0 {
				return &common.APIResponse[any]{
					Status:    common.APIStatus.Invalid,
					Message:   fmt.Sprintf("Lot[%d] required", idx),
					ErrorCode: "INVALID_LOT",
				}
			}
			if product.Quantity <= 0 {
				return &common.APIResponse[any]{
					Status:    common.APIStatus.Invalid,
					Message:   fmt.Sprintf("Quantity[%d] required", idx),
					ErrorCode: "INVALID_QUANTITY",
				}
			}
		} else {
			if product.Price <= 0 {
				return &common.APIResponse[any]{
					Status:    common.APIStatus.Invalid,
					Message:   fmt.Sprintf("Price[%d] required", idx),
					ErrorCode: "INVALID_PRICE",
				}
			}

			if len(product.Unit) == 0 {
				return &common.APIResponse[any]{
					Status:    common.APIStatus.Invalid,
					Message:   fmt.Sprintf("Unit[%d] required", idx),
					ErrorCode: "INVALID_UNIT",
				}
			}
		}

		if product.ProductID <= 0 {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductID[%d] required", idx),
				ErrorCode: "INVALID_PRODUCT_ID",
			}
		}

		if len(product.ProductCode) == 0 {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductCode[%d] required", idx),
				ErrorCode: "INVALID_PRODUCT_CODE",
			}
		}

		if len(m.LegalEntityBranch) == 0 {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   "Legal branch is required",
				ErrorCode: "LEGAL_BRANCH_MISSING",
			}
		}
	}

	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}

// CreateContractAnnexRequest ...
type CreateContractAnnexRequest struct {
	Name         string            `json:"name"`
	ContractCode string            `json:"contractCode"`
	AnnexNumber  string            `json:"annexNumber"`
	ContractDate *time.Time        `json:"contractDate"`
	ExpireDate   *time.Time        `json:"expireDate"`
	Content      string            `json:"content"`
	Products     []*ProductRequest `json:"products"`
	Attachments  []string          `json:"attachments"`
}

// ProductRequest ...
type ProductRequest struct {
	LotID              int64  `json:"lotID"`
	LotName            string `json:"lotName"`
	Quantity           int64  `json:"quantity"`
	ProductID          int64  `json:"productID"`
	ProductCode        string `json:"productCode"`
	Sku                string `json:"sku"`
	ProductReplaceCode string `json:"productReplaceCode"`

	Price int64   `json:"price"`
	Unit  string  `json:"unit"`
	VAT   float64 `json:"vat"`
}

// UpdateContractRequest ...
type UpdateContractRequest struct {
	Code              string     `json:"code"`
	MainContractCode  *string    `json:"mainContractCode"`
	Status            *string    `json:"status"`
	SigningDate       *time.Time `json:"signingDate"`
	ExpireDate        *time.Time `json:"expireDate"`
	Content           *string    `json:"content"`
	Note              *string    `json:"note"`
	Attachments       *[]string  `json:"attachments"`
	ExtendAttachments *[]string  `json:"extendAttachments"`
	Name              *string    `json:"name"`
	BeneficiaryName   *string    `json:"beneficiaryName,omitempty"`
	Address           *string    `json:"address,omitempty"`
	PhoneNumber       *string    `json:"phoneNumber,omitempty"`
	IsStoreDocument   *bool      `json:"isStoreContractDocument,omitempty"`
	Email             *string    `json:"email,omitempty"`

	TaxCode string `json:"taxCode,omitempty"`

	DebtLimit      *int64  `json:"debtLimit"`
	PaymentTerm    *int64  `json:"paymentTerm"`
	ContractNumber *string `json:"contractNumber,omitempty"`

	// legal entity
	LegalEntityCode     *string          `json:"legalEntityCode"`
	LegalEntityName     *string          `json:"legalEntityName"`
	LegalEntityTaxCode  *string          `json:"legalEntityTaxCode"`
	LegalEntityAddress  *string          `json:"legalEntityAddress"`
	LegalEntityEmail    *string          `json:"legalEntityEmail"`
	LegalEntityTel      *string          `json:"legalEntityTel"`
	LegalEntityBankCode *string          `json:"legalEntityBankCode"`
	LegalEntityBank     *LegalEntityBank `json:"legalEntityBank"`

	BudgetUnitCode *string `json:"budgetUnitCode"`

	// Query fields
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"` // Complex query conditions
}

// UpdateContractAnnexRequest ...
type UpdateContractAnnexRequest struct {
	Code         string     `json:"code"`
	ContractDate *time.Time `json:"contractDate"`
	ExpireDate   *time.Time `json:"expireDate"`
	Content      *string    `json:"content"`
	Attachments  *[]string  `json:"attachments"`
	Name         *string    `json:"name"`
}

// ContractAnnexQuery ...
type ContractAnnexQuery struct {
	InvitationOfBid string     `json:"invitationOfBid,omitempty" bson:"itb,omitempty"`
	ContractNumber  string     `json:"contractNumber,omitempty" bson:"contract_number,omitempty"`
	ContractCode    string     `json:"contractCode,omitempty" bson:"contract_code,omitempty"`
	MainContractID  int64      `json:"-" bson:"main_contract_id,omitempty"`
	Code            string     `json:"code,omitempty" bson:"code,omitempty"`
	CreatedFrom     *time.Time `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo       *time.Time `json:"createdTo,omitempty" bson:"-"`
	Status          string     `json:"status,omitempty" bson:"status,omitempty"`
	ComplexQuery    []*bson.M  `json:"-" bson:"$and,omitempty"`
	AnnexIDs        []int64    `json:"annexIDs,omitempty" bson:"-"`
}

type ContractAccountingBalanceResponse struct {
	DocumentDataCode      string `json:"documentDataCode"`
	TotalBalanceTemporary int64  `json:"totalBalanceTemporary"`
	TotalDebtTemporary    int64  `json:"totalDebtTemporary"`
}

type MigrateContractDebtRequest struct {
	ContractCode string `json:"contractCode"`
}

type UpdateContractTaxCodeRequest struct {
	Code    string  `json:"code"`
	TaxCode *string `json:"taxCode"`
}

type UpdateOrderTaxCodeRequest struct {
	OrderCode string  `json:"orderCode"`
	TaxCode   *string `json:"taxCode"`
}

type SyncOrderInvoiceRequest struct {
	OrderCode string `json:"orderCode"`
}

type ForceCancelOrderRequest struct {
	OrderCode string `json:"orderCode"`
}
