package tool

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

func UpdateProductContract(req server.APIRequest, resp server.APIResponder) error {
	var updater model.Product
	if err := req.GetContent(&updater); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if updater.Code == "" {
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "updater.Code == ''",
		})
	}

	oldResp := model.ProductDB.QueryOne(&model.Product{
		Code: updater.Code,
	})
	if !oldResp.Ok() {
		return resp.Respond(oldResp)
	}

	if updater.Quantity == 0 {
		updater.Quantity = oldResp.Data[0].Quantity
	}
	if updater.Price != 0 {
		updater.Amount = updater.Price * updater.Quantity
	}

	return resp.Respond(model.ProductDB.UpdateOne(&model.Product{
		Code: updater.Code,
	}, &updater))
}
