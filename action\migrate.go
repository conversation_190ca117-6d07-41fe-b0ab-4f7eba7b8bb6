package action

import (
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/enum"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateCancelOrderByOrderCode(orderCode string) *common.APIResponse[any] {
	if len(orderCode) == 0 {
		return &common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "OrderCode missing",
		}
	}
	orderRs := model.OrderDB.QueryOne(&model.Order{
		OrderCode: orderCode,
	})
	if orderRs.Ok() {
		orderInfo := orderRs.Data[0]
		if orderInfo.Status == string(enum.OrderState.CANCELLED) {
			return &common.APIResponse[any]{
				Status:  common.APIStatus.Invalid,
				Message: "Order canceled",
			}
		}
		es := revertQuantityProduct(orderInfo)
		if !es.Ok() {
			fmt.Println(es.Message)
			return &es
		}

		updateRs := model.OrderDB.UpdateOne(&model.Order{
			OrderID: orderInfo.OrderID,
		}, &model.Order{
			Status:     string(enum.OrderState.CANCELLED),
			CancelTime: utils.AnyToPointer(time.Now()),
		})
		if !updateRs.Ok() {
			fmt.Println(updateRs.Message)
			return &common.APIResponse[any]{
				Status:  updateRs.Status,
				Message: updateRs.Message,
			}
		}
	}
	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}

func MigrateContractLegalEntityBranch() {
	_id := primitive.NilObjectID
	log.Println("START:MigrateContractLegalEntityBranch")
	defer log.Println("END:MigrateContractLegalEntityBranch")

	for {
		resp := model.ContractDB.Query(model.Contract{
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id}},
			},
		},
			0, 1000, &primitive.D{
				{Key: "_id", Value: 1},
			})

		if resp.Status != common.APIStatus.Ok {
			break
		}

		contractList := resp.Data

		for i := range contractList {
			contract := contractList[i]
			_id = contract.ID

			if contract.LegalEntityCode == "" {
				continue
			}

			if contract.LegalEntityCode != "TAYAU" {
				contract.LegalEntityBranch = "TENDER"
			} else {
				contract.LegalEntityBranch = "TAYAU"
			}
			updateResp := model.ContractDB.UpdateOne(model.Contract{Code: contract.Code}, contract)

			if updateResp.Status != common.APIStatus.Ok {
				log.Println("Update contract failed: ", resp.Message, contract.ContractNumber)
				continue
			}
		}
	}
}

func MigrateProductScope() {
	_id := primitive.NilObjectID
	log.Println("START:Migrate Product Scope")
	defer log.Println("END:Migrate Product Scope")

	for {
		resp := model.ProductDB.Query(model.Product{
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id}},
			},
		},
			0, 1000, &primitive.D{
				{Key: "_id", Value: 1},
			})

		if resp.Status != common.APIStatus.Ok {
			break
		}

		productList := resp.Data

		for i := range productList {
			product := productList[i]
			_id = product.ID

			contractRs := model.ContractDB.QueryOne(&model.Contract{
				Code: product.ContractCode,
			})
			if !contractRs.Ok() {
				continue
			}
			contractInfo := contractRs.Data[0]

			if contractInfo == nil {
				continue
			}

			product.Scope = append(product.Scope, contractInfo.LegalEntityCode)
			product.Scope = utils.Unique(product.Scope)

			updateResp := model.ProductDB.UpdateOne(model.Product{ID: product.ID}, product)

			if updateResp.Status != common.APIStatus.Ok {
				log.Println("Update product failed: ", resp.Message, product.Code)
				continue
			}
		}
	}
}

func MigrateOrderScope() {
	_id := primitive.NilObjectID
	log.Println("START:Migrate Order Scope")
	defer log.Println("END:Migrate Order Scope")

	for {
		resp := model.OrderDB.Query(model.Order{
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id}},
			},
		},
			0, 1000, &primitive.D{
				{Key: "_id", Value: 1},
			})

		if resp.Status != common.APIStatus.Ok {
			break
		}

		orderList := resp.Data

		for i := range orderList {
			order := orderList[i]
			_id = order.ID

			contractRs := model.ContractDB.QueryOne(&model.Contract{
				Code: order.ContractCode,
			})
			if !contractRs.Ok() {
				continue
			}
			contractInfo := contractRs.Data[0]

			if contractInfo == nil {
				continue
			}

			order.Scope = append(order.Scope, contractInfo.LegalEntityCode)
			order.Scope = utils.Unique(order.Scope)

			updateResp := model.OrderDB.UpdateOne(model.Order{OrderID: order.OrderID}, order)

			if updateResp.Status != common.APIStatus.Ok {
				log.Println("Update order failed: ", resp.Message, order.OrderID)
				continue
			}
		}
	}
}

func MigrateContractScope() {
	_id := primitive.NilObjectID
	log.Println("START:Migrate Contract Scope")
	defer log.Println("END:Migrate Contract Scope")

	for {
		resp := model.ContractDB.Query(model.Contract{
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id}},
			},
		},
			0, 1000, &primitive.D{
				{Key: "_id", Value: 1},
			})

		if resp.Status != common.APIStatus.Ok {
			break
		}

		contractList := resp.Data

		for i := range contractList {
			contract := contractList[i]
			_id = contract.ID

			contract.Scope = append(contract.Scope, contract.LegalEntityCode)
			contract.Scope = utils.Unique(contract.Scope)

			updateResp := model.ContractDB.UpdateOne(model.Contract{ContractID: contract.ContractID}, contract)

			if updateResp.Status != common.APIStatus.Ok {
				log.Println("Update contract failed: ", resp.Message, contract.ContractID)
				continue
			}
		}
	}
}

func MigrateBeneficiaryScope() {
	_id := primitive.NilObjectID
	log.Println("START:Migrate Beneficiary Scope")
	defer log.Println("END:Migrate Beneficiary Scope")

	for {
		resp := model.BeneficiaryDB.Query(model.Beneficiary{
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id}},
			},
		},
			0, 1000, &primitive.D{
				{Key: "_id", Value: 1},
			})

		if resp.Status != common.APIStatus.Ok {
			break
		}

		benecifiaryList := resp.Data

		for i := range benecifiaryList {
			beneficiary := benecifiaryList[i]
			_id = beneficiary.ID

			if len(beneficiary.Scope) > 0 {
				continue
			}

			beneficiary.Scope = []string{"MEDX_HCM"}

			updateResp := model.BeneficiaryDB.UpdateOne(model.Beneficiary{Code: beneficiary.Code}, beneficiary)

			if updateResp.Status != common.APIStatus.Ok {
				log.Println("Update contract failed: ", resp.Message, beneficiary.Code)
				continue
			}
		}
	}
}
