package order

import "time"

type OrderMarketplaceResponse struct {
	Data  []*Order `json:"data"`
	Total int64    `json:"total"`
}

type Order struct {
	CreatedByAccountID      int       `json:"createdByAccountId"`
	CreatedTime             time.Time `json:"createdTime"`
	SaleOrderCode           string    `json:"saleOrderCode"`
	CustomerCode            string    `json:"customerCode"`
	CustomerDistrictCode    string    `json:"customerDistrictCode"`
	CustomerEmail           string    `json:"customerEmail"`
	CustomerID              int       `json:"customerId"`
	CustomerLevel           string    `json:"customerLevel"`
	CustomerName            string    `json:"customerName"`
	CustomerPhone           string    `json:"customerPhone"`
	CustomerProvinceCode    string    `json:"customerProvinceCode"`
	CustomerRegionCode      string    `json:"customerRegionCode"`
	CustomerScope           string    `json:"customerScope"`
	CustomerShippingAddress string    `json:"customerShippingAddress"`
	CustomerWardCode        string    `json:"customerWardCode"`
	DeliveryDate            time.Time `json:"deliveryDate"`
	DeliveryMethod          string    `json:"deliveryMethod"`
	DistrictCode            string    `json:"districtCode"`
	EmployeeID              int       `json:"employeeID"`
	Invoice                 struct {
		InvoiceRequest bool `json:"invoiceRequest"`
	} `json:"invoice"`
	LastUpdatedTime  time.Time `json:"lastUpdatedTime"`
	OrderCode        string    `json:"orderCode"`
	OrderID          int       `json:"orderId"`
	PaymentMethod    string    `json:"paymentMethod"`
	PaymentMethodFee int       `json:"paymentMethodFee"`
	Price            int       `json:"price"`
	ProvinceCode     string    `json:"provinceCode"`
	Source           string    `json:"source"`
	Status           string    `json:"status"`
	SystemDisplay    string    `json:"systemDisplay"`
	Tags             []string  `json:"tags"`
	TotalDiscount    int       `json:"totalDiscount"`
	TotalFee         int       `json:"totalFee"`
	TotalItem        int       `json:"totalItem"`
	TotalPrice       int       `json:"totalPrice"`
	TotalQuantity    int       `json:"totalQuantity"`
	WardCode         string    `json:"wardCode"`
	WarehouseCode    string    `json:"warehouseCode"`
	WarehouseName    string    `json:"warehouseName"`
}
