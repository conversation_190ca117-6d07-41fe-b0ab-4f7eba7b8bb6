package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Beneficiary ...
type CreateBeneficiary struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Name        string                 `json:"name,omitempty" bson:"name,omitempty"`
	Address     string                 `json:"address,omitempty" bson:"address,omitempty"`
	PhoneNumber string                 `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
	TaxCode     string                 `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	Status      BeneficiaryStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	Email       string                 `json:"email,omitempty" bson:"email,omitempty"`

	CreatedByID   int64    `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	CreatedByName string   `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	OrgID         int64    `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64    `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	BeneficiaryID int64    `json:"beneficiaryID,omitempty" bson:"beneficiary_id,omitempty"`
	Code          string   `json:"code,omitempty" bson:"code,omitempty"`
	Scope         []string `json:"scope,omitempty" bson:"scope,omitempty"`
	HashTag       string   `json:"-" bson:"hash_tag,omitempty"` // for search
}

type Beneficiary struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code          string                 `json:"code,omitempty" bson:"code,omitempty"`
	CreatedByName string                 `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64                  `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string                 `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64                  `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64                  `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64                  `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	Status        BeneficiaryStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	AccountID     int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	BeneficiaryID int64  `json:"beneficiaryID,omitempty" bson:"beneficiary_id,omitempty"`
	Name          string `json:"name,omitempty" bson:"name,omitempty"`
	Address       string `json:"address,omitempty" bson:"address,omitempty"`
	TaxCode       string `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	PhoneNumber   string `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
	Email         string `json:"email,omitempty" bson:"email,omitempty"`
	IsVerifyTax   *bool  `json:"isVerifyTax,omitempty" bson:"is_verify_tax,omitempty"`

	HashTag string `json:"-" bson:"hash_tag,omitempty"` // for search

	Scope []string `json:"scope,omitempty" bson:"scope,omitempty"`

	// ForQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// BeneficiaryStatusValue ...
type BeneficiaryStatusValue string

// ProductStatusEnt ...
type BeneficiaryStatusEnt struct {
	ACTIVE   BeneficiaryStatusValue
	INACTIVE BeneficiaryStatusValue
}

// BeneficiaryStatus ...
var BeneficiaryStatus = &BeneficiaryStatusEnt{
	"ACTIVE",
	"INACTIVE",
}

// BeneficiaryDB ...
var BeneficiaryDB = &mongodb.Instance[*Beneficiary]{
	ColName: "beneficiaries",
}

// InitBeneficiaryModel ....
func InitBeneficiaryModel(s *mongo.Database) {
	BeneficiaryDB.ApplyDatabase(s)

	t := true
	BeneficiaryDB.CreateIndex(bson.D{
		{Key: "beneficiary_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	BeneficiaryDB.CreateIndex(bson.D{
		{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	BeneficiaryDB.CreateIndex(bson.D{
		{Key: "tax_code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	BeneficiaryDB.CreateIndex(bson.D{
		{Key: "status", Value: 1},
	}, &options.IndexOptions{})
}
