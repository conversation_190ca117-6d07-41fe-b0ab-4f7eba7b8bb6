package action

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// QueryContractAnnexList ...
func QueryContractAnnexList(query *request.ContractAnnexQuery, search string, offset, limit int64, queryOptions request.QueryOption) common.Response {

	if search != "" {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", regexp.QuoteMeta(utils.ParserQ(search))), Options: ""},
		})
	}

	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": query.CreatedTo,
			},
		})
	}

	if len(query.AnnexIDs) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"annex_id": bson.M{
				"$in": utils.Unique(query.AnnexIDs),
			},
		})
	}

	if len(query.ContractCode) > 0 {
		contractResp := model.ContractDB.QueryOne(&model.Contract{
			Code: query.ContractCode,
		})

		if !contractResp.Ok() {
			return contractResp
		}
	}

	resp := model.ContractAnnexDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: -1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.ContractAnnexDB.Count(query).Total
	}
	return resp
}

// CreateContractAnnex ...
func CreateContractAnnex(input *request.CreateContractAnnexRequest, actionSource *request.ActionSource) common.Response {
	if len(input.ContractCode) == 0 {
		return utils.InvalidResponse("Invalid Contract", "INVALID_CONTRACT_CODE")
	}

	if len(input.AnnexNumber) == 0 {
		return utils.InvalidResponse("Invalid annex number", "INVALID_ANNEX_NO")
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: input.ContractCode,
	})
	if !contractResp.Ok() {
		return contractResp
	}

	mainContract := contractResp.Data[0]

	lotIDs := make([]int64, 0)
	for _, item := range input.Products {
		lotIDs = append(lotIDs, item.LotID)
	}
	lotIDs = utils.Unique(lotIDs)
	queryLot := &model.Lot{}

	if mainContract.BidID != nil && *mainContract.BidID > 0 {
		queryLot.BidID = *mainContract.BidID
		bidResp := model.BidDB.QueryOne(&model.Bid{
			BidID: *mainContract.BidID,
		})
		if !bidResp.Ok() {
			return utils.InvalidResponse("Bid not found", "BID_NOT_FOUND")
		}

		bidInfo := bidResp.Data[0]
		if bidInfo.Status != model.BidStatus.WIN {
			return utils.InvalidResponse("Only winning bid can create new contract", "INVALID_BID")
		}
	}

	contractRes := model.ContractAnnexDB.QueryOne(&model.ContractAnnex{
		AnnexNumber: input.AnnexNumber,
		ContractID:  mainContract.ContractID,
	})
	if contractRes.Ok() {
		return utils.InvalidResponse("Contract number existed", "CONTRACT_NUMBER_EXISTED")
	}

	mapLots := make(map[int64]*model.Lot)
	if len(lotIDs) > 0 {
		queryLot.ComplexQuery = append(queryLot.ComplexQuery, &primitive.M{
			"lot_id": bson.M{
				"$in": utils.Unique(lotIDs),
			},
		})
		lotsRes := model.LotDB.Query(queryLot, 0, int64(len(lotIDs)), nil)
		if !lotsRes.Ok() {
			return utils.InvalidResponse("Lot not found", "LOT_NOT_FOUND")
		}

		for _, item := range lotsRes.Data {
			mapLots[item.LotID] = item
		}
	}

	productReplaceCodes := make([]string, 0)
	for _, item := range input.Products {
		if len(item.ProductReplaceCode) > 0 {
			productReplaceCodes = append(productReplaceCodes, item.ProductReplaceCode)
		}
	}

	productReplaceCodes = utils.Unique(productReplaceCodes)
	mapProductReplaces := make(map[string]*model.Product)
	if len(productReplaceCodes) > 0 {
		productsRs := model.ProductDB.QueryOne(&model.Product{
			MainContractID: mainContract.ContractID,
			ComplexQuery: []*primitive.M{
				{
					"code": bson.M{
						"$in": productReplaceCodes,
					},
				},
			},
		})
		if !productsRs.Ok() || len(productsRs.Data) != len(productReplaceCodes) {
			return utils.InvalidResponse("Product need replace to be not found", "PRODUCT_NOT_FOUND")
		}
		for _, item := range productsRs.Data {
			mapProductReplaces[item.Code] = item
		}
	}

	annexInfo := &model.ContractAnnex{
		Name:         input.Name,
		AnnexNumber:  input.AnnexNumber,
		ContractCode: mainContract.Code,
		ContractID:   mainContract.ContractID,
		Content:      input.Content,
		ContractDate: input.ContractDate,
		ExpireDate:   input.ExpireDate,
		Attachments:  input.Attachments,
		OrgID:        mainContract.OrgID,
		EntityID:     mainContract.EntityID,
	}

	if mainContract.BidID != nil {
		annexInfo.BidID = mainContract.BidID
	}

	annexInfo.HashTag = strings.Replace(utils.NormalizeString(annexInfo.Name), " ", "-", -1)

	if actionSource != nil {
		if actionSource.Account != nil {
			annexInfo.CreatedByID = actionSource.Account.AccountID
			annexInfo.CreatedByName = actionSource.Account.FullName
		}
	}

	annexInfo.AnnexID = model.GenContractID()
	annexInfo.Code = model.ConvertToCode(annexInfo.AnnexID)

	annexCreateResp := model.ContractAnnexDB.Create(annexInfo)
	if !annexCreateResp.Ok() {
		return annexCreateResp
	}

	if len(annexInfo.Attachments) > 0 {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: annexInfo.Attachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": annexInfo.AnnexNumber,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	if len(mapLots) > 0 {
		// product mapping
		products := make([]*model.Product, 0)
		productsResetRemain := make([]string, 0)
		contractAnnex := annexCreateResp.Data[0]

		for _, item := range input.Products {
			productItem := &model.Product{
				LotID: item.LotID,
				Code:  model.ConvertToCode(model.GenBidProductID()),
			}

			lotItem := mapLots[item.LotID]
			if lotItem == nil {
				continue
			}

			productItem.GuaranteeAmount = lotItem.GuaranteeAmount
			productItem.ContractID = contractAnnex.AnnexID
			productItem.ContractCode = contractAnnex.Code
			productItem.MainContractID = mainContract.ContractID
			productItem.ContractType = "CONTRACT_ANNEX"
			productItem.ProductID = item.ProductID
			productItem.ProductCode = item.ProductCode
			productItem.ProductName = item.LotName
			productItem.Quantity = item.Quantity
			productItem.QuantityInitial = item.Quantity
			productItem.QuantityRemain = item.Quantity
			productItem.Price = lotItem.LotPrice
			productItem.CreatedByID = contractAnnex.CreatedByID
			productItem.CreatedByName = contractAnnex.CreatedByName
			productItem.OrgID = contractAnnex.OrgID
			productItem.EntityID = contractAnnex.EntityID
			productItem.GroupMedicine = lotItem.GroupMedicine
			productItem.Unit = lotItem.Unit
			productItem.RegistrationNo = lotItem.RegistrationNo
			productItem.Volume = lotItem.Volume
			productItem.VAT = lotItem.VAT

			if item.Price > 0 {
				productItem.Price = item.Price
			}

			productItem.Amount = productItem.Quantity * productItem.Price

			// handle add new or replace
			if len(item.ProductReplaceCode) > 0 {
				if nestProduct := mapProductReplaces[item.ProductReplaceCode]; nestProduct != nil {
					productItem.QuantityRemain = nestProduct.QuantityRemain
					productItem.IsRemove = true
					if item.Quantity != productItem.QuantityRemain {
						productItem.QuantityRemain = item.Quantity
						productItem.Quantity = nestProduct.Quantity
					}

					productsResetRemain = append(productsResetRemain, nestProduct.Code)
				}
			}

			products = append(products, productItem)
		}

		if len(products) > 0 {
			productsRes := model.ProductDB.CreateManyWithCtx(context.TODO(), products)
			if !productsRes.Ok() {
				return productsRes
			}

			// for _, product := range products {
			// 	if product.IsRemove {
			// 		continue
			// 	}
			// 	_ = model.ProductDB.UpdateOneWithOperator(&model.Product{
			// 		ContractID: mainContract.ContractID,
			// 		LotID:      product.LotID,
			// 	}, bson.M{
			// 		"$inc": bson.M{"quantity": product.Quantity, "quantity_remain": product.Quantity},
			// 	})
			// }
		}

		productsResetRemain = utils.Unique(productsResetRemain)
		if len(productsResetRemain) > 0 {
			_ = model.ProductDB.UpdateManyWithCtx(context.TODO(), &model.Product{
				MainContractID: mainContract.ContractID,
				ComplexQuery: []*primitive.M{
					{
						"code": bson.M{
							"$in": productsResetRemain,
						},
					},
				}}, bson.M{
				"quantity_remain": 0,
			})
		}
	}

	return annexCreateResp
}

// GetContractAnnexInfo ...
func GetContractAnnexInfo(contractCode, code string) common.Response {
	if contractCode == "" || code == "" {
		return utils.InvalidResponse("Invalid ContractCode", "INVALID_CONTRACT_CODE")
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: contractCode,
	})
	if !contractResp.Ok() {
		return contractResp
	}

	mainContract := contractResp.Data[0]

	query := &model.ContractAnnex{
		ContractID: mainContract.ContractID,
		Code:       code,
	}

	return model.ContractAnnexDB.QueryOne(query)
}

// UpdateContractAnnex ...
func UpdateContractAnnex(input *request.UpdateContractAnnexRequest, actionSource *request.ActionSource) common.Response {
	if input.Code == "" {
		return utils.InvalidResponse("Invalid Contract Code", "INVALID_CONTRACT_CODE")
	}

	contractAnnexResp := model.ContractAnnexDB.QueryOne(&model.ContractAnnex{
		Code: input.Code,
	})

	if !contractAnnexResp.Ok() {
		return utils.NotfoundDataResponse[any]("Not found any matched contract", "CONTRACT_NOT_FOUND")
	}

	annexInfo := contractAnnexResp.Data[0]

	updater := &model.ContractAnnex{}

	if input.ExpireDate != nil {
		updater.ExpireDate = input.ExpireDate
	}

	if input.ContractDate != nil {
		updater.ContractDate = input.ContractDate
	}

	if input.Name != nil {
		updater.Name = *input.Name
	}

	if input.Content != nil {
		updater.Content = *input.Content
	}

	if input.Attachments != nil {
		updater.Attachments = *input.Attachments
	}

	resp := model.ContractAnnexDB.UpdateOne(model.ContractAnnex{
		AnnexID: annexInfo.AnnexID,
	}, updater)
	if !resp.Ok() {
		return resp
	}

	if input.Attachments != nil && len(*input.Attachments) > 0 && utils.CompareArrays(annexInfo.Attachments, *input.Attachments) {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: *input.Attachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": annexInfo.AnnexNumber,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	return resp
}
