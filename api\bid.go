package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// CreateBid ...
func CreateBid(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateBidRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.CreateBid(&input, actionSource))
}

// GetBidInfo ...
func GetBidInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()
	var bidID = utils.ParseInt64(params["bidID"], 0)
	return resp.Respond(action.GetBidInfo(bidID))
}

// UpdateBid ...
func UpdateBid(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateBidRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.UpdateBid(&input, actionSource))
}

// QueryBidList ...
func QueryBidList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.BidQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	return resp.Respond(action.QueryBidList(&input.Q, input.Search, input.Offset, input.Limit, input.Option))
}
