package request

import "time"

type ActionSource struct {
	// Account info
	Account *Account `json:"account"`
	// Current session
	Session   *Session    `json:"session"`
	UserRoles []*UserRole `json:"userRoles"` // Roles associated with the user
}

type Account struct {
	// Account info
	AccountID           int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	Username            string `json:"username,omitempty" bson:"username,omitempty"` // random username
	Email               string `json:"email,omitempty" bson:"email,omitempty"`
	PhoneNumber         string `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
	PhoneNumberVerified *bool  `json:"phoneNumberVerified,omitempty" bson:"phone_number_verified,omitempty"`
	CountryCode         string `json:"countryCode,omitempty" bson:"country_code,omitempty"`

	// Basic info
	FullName  string `json:"fullname,omitempty" bson:"fullname,omitempty"`
	FirstName string `json:"firstName,omitempty" bson:"first_name,omitempty"`
	LastName  string `json:"lastName,omitempty" bson:"last_name,omitempty"`
	Type      string `json:"type"` // Type of account
}

// Session ...
type Session struct {
	AccountID int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	Token     string `json:"token,omitempty" bson:"token,omitempty"`
	TokenType string `json:"tokenType,omitempty" bson:"token_type,omitempty"`
	AppID     int64  `json:"appID,omitempty" bson:"app_id,omitempty"`
	AppCode   string `json:"appCode,omitempty" bson:"app_code,omitempty"`
	OrgID     int64  `json:"orgID,omitempty"  bson:"org_id,omitempty"`
	EntityID  int64  `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	ClientID  string `json:"clientID,omitempty" bson:"-"`
}

type UserRole struct {
	OrgID    int64  `json:"orgID"`    // Organization ID
	EntityID int64  `json:"entityID"` // Entity ID
	RoleCode string `json:"roleCode"` // Code representing the role
	RoleName string `json:"roleName"` // Name of the role
	Entity   Entity `json:"entity"`   // Associated entity details
}

type Entity struct {
	AppID       int            `json:"appID,omitempty"`       // Application ID
	Attributes  map[string]any `json:"attributes,omitempty"`  // Additional attributes
	CacheTime   time.Time      `json:"cacheTime,omitempty"`   // Cache timestamp
	Code        string         `json:"code,omitempty"`        // Entity code
	CreatedTime time.Time      `json:"createdTime,omitempty"` // Creation timestamp
	EntityID    int            `json:"entityID,omitempty"`    // Entity ID
	Name        string         `json:"name,omitempty"`        // Name of the entity
	OrgID       int            `json:"orgID,omitempty"`       // Organization ID
	Status      string         `json:"status,omitempty"`      // Status of the entity
	Type        string         `json:"type,omitempty"`        // Type of entity
}
