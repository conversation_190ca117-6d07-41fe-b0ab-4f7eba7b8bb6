package oms

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout             = 60 * time.Second
	pathCreateOrder            = "/oms/order/v1/order"
	pathGetOrderID             = "/oms/order/v1/order-gen-id"
	pathRequestCancelOrder     = "/oms/order/v1/cancel-order"
	pathRequestInvoiceExchange = "/oms/order/v1/invoice-exchange"
)

type client struct {
	host          string
	defaultClient *sdk_client.RestClient
	headers       map[string]string
}

var proxyOmsClient *client

// InitProxyOmsClient ...
func InitProxyOmsClient(db *mongo.Database) {
	proxyOmsClient = &client{
		host: conf.Config.BuymedPlatformClient.Host,
		headers: map[string]string{
			"Authorization": conf.Config.BuymedPlatformClient.Authorization,
		},
		defaultClient: sdk_client.NewRESTClient(conf.Config.BuymedPlatformClient.Host, "oms_client", defaultTimeout, 0, 0),
	}
	proxyOmsClient.defaultClient.SetDBLog(db)
	proxyOmsClient.defaultClient.SetLoggerName("oms_client")
}

// CreateOrder ...
func CreateOrder(input *job.OrderOms) error {
	resp, err := proxyOmsClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, proxyOmsClient.headers, nil, input, pathCreateOrder)
	if err != nil {
		return err
	}

	fmt.Printf("%v\n", resp.Body)
	if resp.Code != http.StatusOK {
		return errors.New("error: fail to call create order")
	}

	return nil
}

func GetOrderID() (int64, string, error) {
	resp, err := proxyOmsClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Get, proxyOmsClient.headers, nil, nil, pathGetOrderID)
	if err != nil {
		return 0, "", err
	}

	fmt.Printf("%v\n", resp.Body)
	if resp.Code != http.StatusOK {
		return 0, "", errors.New("error: fail to call order gen id")
	}

	var data *common.APIResponse[*GenOrderIDResponse]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return 0, "", errors.New("error: fail to read data because " + err.Error())
	}

	if len(data.Data) == 0 {
		return 0, "", errors.New("error: fail to read data empty ")
	}

	return data.Data[0].ID, data.Data[0].Code, nil
}

func RequestCancelOrder(input *CancelOrderRequest) error {
	resp, err := proxyOmsClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Put, proxyOmsClient.headers, nil, input, pathRequestCancelOrder)
	if err != nil {
		return err
	}

	fmt.Printf("%v\n", resp.Body)

	var data *common.APIResponse[any]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return errors.New("error: fail to read data because " + err.Error())
	}

	if resp.Code != http.StatusOK {
		return errors.New("error: " + data.Message)
	}

	return nil
}

func RequestInvoiceExchange(payload *InvoiceExchangeRequest) (interface{}, error) {
	resp, err := proxyOmsClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, proxyOmsClient.headers, nil, payload, pathRequestInvoiceExchange)
	if err != nil {
		return "", err
	}

	if resp.Code != http.StatusOK {
		return "", errors.New("error: fail to call request export invoice")
	}

	var data *common.APIResponse[any]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return "", errors.New("error: fail to read data")
	}

	return nil, nil
}
