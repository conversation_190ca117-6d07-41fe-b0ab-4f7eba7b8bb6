package job

import (
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue/basic_queue"
	"go.mongodb.org/mongo-driver/mongo"
)

type OrderOms struct {
	Channel            string `json:"channel"`
	Currency           string `json:"currency"`
	OrderID            int64  `json:"orderID"`
	OrderCode          string `json:"orderCode"`
	InternalOrderID    int64  `json:"internalOrderID"`
	InternalOrderCode  string `json:"internalOrderCode"`
	Status             string `json:"status"`
	CreatedByAccountId int64  `json:"createdByAccountId"`
	CreatedByName      string `json:"createdByName"`
	OrgID              int64  `json:"orgID,omitempty"`
	EntityID           int64  `json:"entityID,omitempty"`

	AccountID               int64   `json:"accountId"`
	CustomerID              int64   `json:"customerId"`
	CustomerType            string  `json:"customerType"`
	CustomerCode            string  `json:"customerCode"`
	CustomerName            string  `json:"customerName"`
	CustomerPhone           string  `json:"customerPhone"`
	CustomerEmail           *string `json:"customerEmail"`
	CustomerShippingAddress string  `json:"customerShippingAddress"`
	CustomerDistrictCode    string  `json:"customerDistrictCode"`
	CustomerWardCode        string  `json:"customerWardCode"`
	CustomerProvinceCode    string  `json:"customerProvinceCode"`
	CustomerRegionCode      string  `json:"customerRegionCode"`

	Price                       int64                 `json:"price"`
	Cod                         *int64                `json:"cod"`
	PaymentMethod               string                `json:"paymentMethod"`
	PaymentMethodPercentage     *float64              `json:"paymentMethodPercentage"`
	PaymentMethodDiscountAmount int64                 `json:"paymentMethodDiscountAmount"`
	DeliveryMethod              string                `json:"deliveryMethod"`
	DeliveryMethodFee           *float64              `json:"deliveryMethodFee"`
	DeliveryDate                *time.Time            `json:"deliveryDate"`
	ConfirmationDate            *time.Time            `json:"confirmationDate"`
	CreatedTime                 *time.Time            `json:"createdTime"`
	Source                      string                `json:"source"`
	Note                        *string               `json:"note"`
	ConfirmType                 string                `json:"confirmType"`
	VoucherAmount               int64                 `json:"voucherAmount"`
	ExtraFee                    *int64                `json:"extraFee"`
	TotalPrice                  int64                 `json:"totalPrice"`
	TotalDiscount               *int                  `json:"totalDiscount"`
	TotalFee                    *int                  `json:"totalFee"`
	Tags                        []string              `json:"tags"`
	OrderItems                  []*OrderItem          `json:"orderItems"`
	Invoice                     *model.InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`
	AccountingRefCode           string                `json:"accountingRefCode,omitempty"`
	CanExportInvoice            bool                  `json:"canExportInvoice"`

	Scope []string `json:"scope" bson:"scope,omitempty"` // Access scope
}

type OrderItem struct {
	InternalOrderID   int64                    `json:"internalOrderID,omitempty" bson:"internal_order_id,omitempty"`
	InternalOrderCode string                   `json:"internalOrderCode,omitempty" bson:"internal_order_code,omitempty"`
	Sku               string                   `json:"sku"`
	IsImportant       *bool                    `json:"isImportant,omitempty"`
	Type              string                   `json:"type,omitempty"`
	SellerCode        string                   `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerClass       string                   `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	ProductName       string                   `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode       string                   `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID         int64                    `json:"productID,omitempty" bson:"product_id,omitempty"`
	SkuStatus         string                   `json:"skuStatus"`
	SkuPriceType      string                   `json:"skuPriceType,omitempty"`
	VAT               *int                     `json:"vat,omitempty"`
	Price             int                      `json:"price,omitempty" bson:"price,omitempty,omitempty"`
	TotalPrice        int                      `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	Quantity          int                      `json:"quantity,omitempty" bson:"quantity,omitempty"`
	Unit              string                   `json:"unit,omitempty" bson:"unit,omitempty"`
	UnitName          string                   `json:"unitName,omitempty" bson:"unit_name,omitempty"`
	ConfigUnit        *pim.ProductUnitResponse `json:"configUnit,omitempty" bson:"config_unit,omitempty"`
	Volume            string                   `json:"volume,omitempty" bson:"volume,omitempty"`
}

var JobSyncOrderToOMS *basic_queue.Queue[*OrderOms]

func InitJobSyncOrderToOMS(session *mongo.Database) {

	JobSyncOrderToOMS = basic_queue.New[*OrderOms](
		"tender_sync_to_oms", &db_queue.Configuration{
			CurVersionTimeoutS:  20 * 60, // 20 min
			OldVersionTimeoutS:  10 * 60, // 10 min
			LogSize:             5,
			MaximumWaitToRetryS: 3, // 3 secs
			ChannelCount:        4,
			ConsumedExpiredTime: time.Duration(7*24) * time.Hour,
			FailThreshold:       3,
			UniqueItem:          true,
		},
	)

	JobSyncOrderToOMS.Init(session)
}
