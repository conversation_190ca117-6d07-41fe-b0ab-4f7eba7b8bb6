package utils

import (
	"bytes"
	"crypto/sha256"
	"encoding/gob"
)

// AnyToPointer ...
func AnyToPointer[T any](src T) *T {
	return &src
}

// Unique ...
func Unique[T comparable](arr []T) []T {
	u := make([]T, 0)
	m := make(map[T]bool)
	for _, val := range arr {
		if _, ok := m[val]; !ok {
			m[val] = true
			u = append(u, val)
		}
	}
	return u
}

// CompareArrays ...
func CompareArrays[T comparable](arr1, arr2 []T) bool {
	if len(arr1) != len(arr2) {
		return false
	}

	for i := range arr1 {
		if arr1[i] != arr2[i] {
			return false
		}
	}

	return true
}

// ConvertToAny ...
func ConvertToAny[T any](s []T) []any {
	result := make([]any, len(s))
	for i, v := range s {
		result[i] = v
	}
	return result
}

func CalculateCheckSum(data interface{}) ([]byte, error) {
	// Serialize the struct to a byte slice
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	if err := enc.Encode(data); err != nil {
		return nil, err
	}

	// Calculate the SHA256 checksum
	hash := sha256.New()
	_, err := hash.Write(buf.Bytes())
	if err != nil {
		return nil, err
	}
	return hash.Sum(nil), nil
}

func Contains[T string | int64 | int](s []T, e T) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}
