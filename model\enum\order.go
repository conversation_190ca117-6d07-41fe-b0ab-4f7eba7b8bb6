package enum

const (
	CREATE_ORDER = "CREATE_ORDER"
	CANCEL_ORDER = "CANCEL_ORDER"
)

type OrderStateValue string

type orderStateValue struct {
	WAIT_TO_CONFIRM OrderStateValue
	CONFIRMED       OrderStateValue
	PROCESSING      OrderStateValue
	WAIT_TO_DELIVER OrderStateValue
	DELIVERING      OrderStateValue
	DELIVERED       OrderStateValue
	RETURNED        OrderStateValue
	COMPLETED       OrderStateValue

	DELETING  OrderStateValue
	CANCELLED OrderStateValue
}

var OrderState = &orderStateValue{
	"WAIT_TO_CONFIRM",
	"CONFIRMED",
	"PROCESSING",
	"WAIT_TO_DELIVER",
	"DELIVERING",
	"DELIVERED",
	"RETURNED",
	"COMPLETED",

	"DELETING",
	"CANCELLED",
}
