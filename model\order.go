package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Order ...
type Order struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64  `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64  `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64  `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	Status        string `json:"status,omitempty" bson:"status,omitempty"`

	EmployeeID              int64  `json:"employeeID,omitempty" bson:"employee_id,omitempty"`
	AccountID               int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	ReceiverID              int64  `json:"receiverID,omitempty" bson:"receiver_id,omitempty"`
	ReceiverCode            string `json:"receiverCode,omitempty" bson:"receiver_code,omitempty"`
	ReceiverName            string `json:"receiverName,omitempty" bson:"receiver_name,omitempty"`
	ReceiverPhoneNumber     string `json:"receiverPhoneNumber,omitempty" bson:"receiver_phone_number,omitempty"`
	ReceiverShippingAddress string `json:"receiverShippingAddress,omitempty" bson:"receiver_shipping_address,omitempty"`
	ReceiverEmail           string `json:"receiverEmail,omitempty" bson:"receiver_email,omitempty"`
	RegionCode              string `json:"regionCode,omitempty" bson:"region_code,omitempty"`
	ProvinceCode            string `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	DistrictCode            string `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	WardCode                string `json:"wardCode,omitempty" bson:"ward_code,omitempty"`

	OrderID           int64  `json:"orderID,omitempty" bson:"order_id,omitempty"`
	OrderCode         string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	Currency          string `json:"currency,omitempty" bson:"currency,omitempty"`
	ContractCode      string `json:"contractCode,omitempty" bson:"contract_code,omitempty"`
	BidID             int64  `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	AccountingRefCode string `json:"accountingRefCode,omitempty" bson:"accounting_ref_code,omitempty"`
	Source            string `json:"source,omitempty" bson:"source,omitempty"`
	Note              string `json:"note,omitempty" bson:"note,omitempty"`
	SaleOrderCode     string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`

	SaleOrderCreatedTime *time.Time `json:"saleOrderCreatedTime,omitempty" bson:"sale_order_created_time,omitempty"`
	ConfirmationDate     *time.Time `json:"confirmationDate,omitempty" bson:"confirmation_date,omitempty"` // ngày xác nhận -- field cũ

	Invoice           *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`
	PaymentMethod     string          `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	DeliveryMethod    string          `json:"deliverMethod,omitempty" bson:"deliver_method,omitempty"`
	InvoiceNo         *string         `json:"invoiceNo,omitempty" bson:"invoice_no,omitempty"`
	InvoiceExchangeNo *string         `json:"invoiceExchangeNo,omitempty" bson:"invoice_exchange_no,omitempty"`
	PaidTime          *time.Time      `json:"paidTime,omitempty" bson:"paid_time,omitempty"`
	PaidAmount        *int64          `json:"paidAmount,omitempty" bson:"paid_amount,omitempty"`
	PaymentStatus     *string         `json:"paymentStatus,omitempty" bson:"payment_status,omitempty"`

	DeliveryDate       *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`
	DeliveredTime      *time.Time `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	OutboundDate       *time.Time `json:"outboundDate,omitempty" bson:"outbound_date,omitempty"`            // ngày xuất kho
	CompletedTime      *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`          // thời gian hoàn tất đơn hàng -- status = completed
	CompletedDebtTime  *time.Time `json:"completedDebtTime,omitempty" bson:"completed_debt_time,omitempty"` // thời gian hoàn tất công nợ
	AskExportInvoice   *time.Time `json:"askExportInvoice,omitempty" bson:"ask_export_invoice,omitempty"`
	AskExportInvoiceBy *int64     `json:"askExportInvoiceBy,omitempty" bson:"ask_export_invoice_by,omitempty"`
	CancelTime         *time.Time `json:"cancelTime,omitempty" bson:"cancel_time,omitempty"` // ngày huỷ

	CanExportInvoice *bool  `json:"canExportInvoice,omitempty" bson:"can_export_invoice,omitempty"`
	TotalAmount      int64  `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	COD              int64  `json:"cod,omitempty" bson:"cod,omitempty"`
	ActualAmount     *int64 `json:"actualAmount,omitempty" bson:"actual_amount,omitempty"` // tổng tiền hàng thực tế

	Items        []*OrderItem `json:"items,omitempty" bson:"-"`
	ContractInfo *Contract    `json:"contractInfo,omitempty" bson:"-"`

	Scope []string `json:"scope,omitempty" bson:"scope,omitempty"`

	// ForQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// InvoiceRequest ...
type InvoiceRequest struct {
	CompanyName    string `json:"companyName,omitempty" bson:"company_name,omitempty"`
	TaxCode        string `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	CompanyAddress string `json:"companyAddress,omitempty" bson:"company_address,omitempty"`
	Email          string `json:"email,omitempty" bson:"email,omitempty"`
	InvoiceRequest *bool  `json:"invoiceRequest,omitempty" bson:"invoice_request,omitempty"`

	LegalEntityCode        string `json:"legalEntityCode,omitempty" bson:"legal_entity_code,omitempty"`
	LegalEntityName        string `json:"legalEntityName,omitempty" bson:"legal_entity_name,omitempty"`
	LegalEntityTaxCode     string `json:"legalEntityTaxCode,omitempty" bson:"legal_entity_tax_code,omitempty"`
	LegalEntityAddress     string `json:"legalEntityAddress,omitempty" bson:"legal_entity_address,omitempty"`
	LegalEntityEmail       string `json:"legalEntityEmail,omitempty" bson:"legal_entity_email,omitempty"`
	LegalEntityTel         string `json:"legalEntityTel,omitempty" bson:"legal_entity_tel,omitempty"`
	LegalEntityBankCode    string `json:"legalEntityBankCode,omitempty" bson:"legal_entity_bank_code,omitempty"`
	LegalEntityBankName    string `json:"legalEntityBankName,omitempty" bson:"legal_entity_bank_name,omitempty"`
	LegalEntityBankBranch  string `json:"legalEntityBankBranch,omitempty" bson:"legal_entity_bank_branch,omitempty"`
	LegalEntityBankAccount string `json:"legalEntityBankAccount,omitempty" bson:"legal_entity_bank_account,omitempty"`
	AccountingRefCode      string `json:"accountingRefCode,omitempty" bson:"accounting_ref_code,omitempty"`
	ContractCode           string `json:"contractCode,omitempty" bson:"contract_code,omitempty"`
	Version                string `json:"version,omitempty" bson:"version,omitempty"`
	BudgetUnitCode         string `json:"budgetUnitCode,omitempty" bson:"budget_unit_code,omitempty"`
}

// OrderDB ...
var OrderDB = &mongodb.Instance[*Order]{
	ColName: "orders",
}

// InitOrderModel ....
func InitOrderModel(s *mongo.Database) {
	OrderDB.ApplyDatabase(s)

	t := true
	OrderDB.CreateIndex(bson.D{
		{Key: "order_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	OrderDB.CreateIndex(bson.D{
		{Key: "order_code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	OrderDB.CreateIndex(bson.D{
		{Key: "status", Value: 1},
	}, &options.IndexOptions{})

	OrderDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	OrderDB.CreateIndex(bson.D{
		{Key: "contract_code", Value: 1},
	}, &options.IndexOptions{})

	OrderDB.CreateIndex(bson.D{
		{Key: "receiver_code", Value: 1},
	}, &options.IndexOptions{})
}
