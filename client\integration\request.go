package integration

import "time"

type CreateContractTransactionRequest struct {
	Channel                 string     `json:"channel"`
	CountryCode             string     `json:"countryCode"`
	OrderID                 int        `json:"orderId"`
	CustomerName            string     `json:"customerName"`
	DeliverMethod           string     `json:"deliverMethod"`
	Price                   int        `json:"price"`
	Currency                string     `json:"currency"`
	OrderCode               string     `json:"orderCode"`
	CustomerID              int        `json:"customerId"`
	ConfirmationDate        *time.Time `json:"confirmationDate"`
	Tags                    []string   `json:"tags"`
	CreatedTime             *time.Time `json:"createdTime"`
	CreatedByID             string     `json:"createdById"`
	CustomerType            string     `json:"customerType"`
	CustomerCode            string     `json:"customerCode"`
	TotalPrice              int        `json:"totalPrice"`
	CustomerDistrictCode    string     `json:"customerDistrictCode"`
	CustomerWardCode        string     `json:"customerWardCode"`
	InternalOrderID         int64      `json:"internalOrderId"`
	InternalOrderCode       string     `json:"internalOrderCode"`
	CustomerProvinceCode    string     `json:"customerProvinceCode"`
	Status                  string     `json:"status"`
	AccountID               int64      `json:"accountId"`
	PaymentMethod           string     `json:"paymentMethod"`
	CustomerPhone           string     `json:"customerPhone"`
	Source                  string     `json:"source"`
	Note                    string     `json:"note"`
	CustomerShippingAddress string     `json:"customerShippingAddress"`
	DocumentDataCode        string     `json:"documentDataCode"`
	PartyBCode              string     `json:"partyBCode"`
}
