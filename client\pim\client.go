package pim

import (
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout        = 30 * time.Second
	pathGetConversionUnit = "/pim/convertwiz/v1/product-unit/search"
	pathExchangeUnit      = "/pim/convertwiz/v1/product-unit/convert"
)

type client struct {
	host          string
	defaultClient *sdk_client.RestClient
	headers       map[string]string
}

var pimClient *client

// InitPimClient ...
func InitPimClient(db *mongo.Database) {
	pimClient = &client{
		host: conf.Config.BuymedPlatformClient.Host,
		headers: map[string]string{
			"Authorization": conf.Config.BuymedPlatformClient.Authorization,
		},
		defaultClient: sdk_client.NewRESTClient(conf.Config.BuymedPlatformClient.Host, "pim_client", defaultTimeout, 0, 0),
	}

	pimClient.defaultClient.SetDBLog(db)
}

// GetConversionUnit ...
func GetConversionUnit(productIDs []int64, limitValue int64) ([]*ConversionUnit, error) {
	payload := &getConversionUnitRequest{
		ProductIDs:  productIDs,
		CountryCode: "VN",
	}

	if limitValue > 0 {
		payload.Value = limitValue
	}

	resp, err := pimClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, pimClient.headers, nil, &queryGetConversionUnitRequest{payload}, pathGetConversionUnit)
	if err != nil {
		return nil, err
	}

	if resp.Code != http.StatusOK {
		return nil, errors.New("error: fail to call api search product unit")
	}

	var data *queryGetConversionUnitResponse
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return nil, errors.New("error: fail to read data")
	}

	if len(data.Data) == 0 {
		return nil, errors.New("error: data not found")
	}

	return data.Data, nil
}

func ExchangeUnit(products []*ProductUnit) (*ConversionUnitResponse, error) {
	payload := &ConversionUnitRequest{
		Products:    products,
		CountryCode: "VN",
	}

	resp, err := pimClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, pimClient.headers, nil, payload, pathExchangeUnit)
	if err != nil {
		return nil, err
	}

	if resp.Code != http.StatusOK {
		return nil, errors.New("error: fail to call api search unit")
	}

	var data *baseResponse[*ConversionUnitResponse]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return nil, errors.New("error: fail to read data")
	}

	if len(data.Data) == 0 {
		return nil, errors.New("error: data not found")
	}

	return data.Data[0], nil
}
