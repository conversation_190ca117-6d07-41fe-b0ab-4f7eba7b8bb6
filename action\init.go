package action

import (
	"errors"

	invoice_v2 "gitlab.buymed.tech/buymed.com/tender/core-tender/client/invoice-v2"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
)

func CheckTax(taxCode string) error {
	resp := invoice_v2.CheckTax(
		request.BuyerTaxGOV{
			BuyerTaxCode: taxCode,
			IsSync:       true,
		},
	)

	if !resp.Ok() {
		return errors.New(resp.Message)
	}

	data := resp.Data[0]
	if data.BuyerTaxGOVStatus != "VALID" {
		return errors.New(data.BuyerTaxGOVDescription)
	}

	return nil
}
