package action

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/action/event"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/oms"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/order"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/enum"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// QueryOrderList ...
func QueryOrderList(query *request.OrderQuery, _ string, offset, limit int64, queryOptions request.QueryOption, actionSource *request.ActionSource) common.Response {

	// Generate query scope based on action source
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	resp, total, err := order.GetListOrders(query.Status, query.OrderID, offset, limit)
	if err != nil {
		return utils.ErrorResponse(err.Error(), "SEARCH_ERROR")
	}
	return &common.APIResponse[*order.Order]{
		Status: common.APIStatus.Ok,
		Data:   resp,
		Total:  total,
	}
}

func RemoveOrderDraft(orderCode string, actionSource *request.ActionSource) common.Response {
	if len(orderCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Order Code missing",
			ErrorCode: "INVALID_ORDER_CODE",
		}
	}

	orderResp := model.OrderDB.QueryOne(&model.Order{
		OrderCode: orderCode,
		Status:    string(enum.OrderState.WAIT_TO_CONFIRM),
	})

	if !orderResp.Ok() {
		return orderResp
	}

	order := orderResp.Data[0]

	orderItemResult := model.OrderItemDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 1000, nil)
	if !orderItemResult.Ok() {
		return orderItemResult
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code:   order.ContractCode,
		Status: model.ContractStatus.ACTIVE,
	})
	if !contractResp.Ok() {
		return contractResp
	}

	// lock
	orderUpdateResp := model.OrderDB.UpdateOne(&model.Order{OrderCode: order.OrderCode, Status: string(enum.OrderState.WAIT_TO_CONFIRM)}, &model.Order{Status: string(enum.OrderState.DELETING)})
	if !orderUpdateResp.Ok() {
		return orderUpdateResp
	}

	orderItems := orderItemResult.Data
	contract := contractResp.Data[0]
	for _, orderItem := range orderItems {
		model.ProductDB.UpdateOneWithOperator(&model.Product{
			Code:           orderItem.ContractProductCode,
			MainContractID: contract.ContractID,
			ComplexQuery: []*primitive.M{
				{
					"quantity_processing": bson.M{
						"$gte": orderItem.Quantity,
					},
				},
			},
		}, bson.M{
			"$inc": bson.M{
				"quantity_remain":     orderItem.Quantity,
				"quantity_processing": -orderItem.Quantity,
			},
		})
		model.OrderItemDB.Delete(&model.OrderItem{ItemID: orderItem.ItemID})
	}

	return model.OrderDB.Delete(&model.Order{OrderCode: order.OrderCode, Status: string(enum.OrderState.DELETING)})
}

// CreateOrder ...
func CreateOrder(input *request.CreateOrderRequest, actionSource *request.ActionSource) common.Response {
	es := input.Validate()
	if !es.Ok() {
		return es
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code:   input.ContractCode,
		Status: model.ContractStatus.ACTIVE,
	})
	if !contractResp.Ok() {
		return contractResp
	}

	contract := contractResp.Data[0]

	// permission check
	scope := []string{contract.LegalEntityCode}
	err := ValidateOrderContractScope(*actionSource, contract)
	if err != nil { // allow admin
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   "You can't create product with this legal entity",
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	if contract.AccountingRefCode == nil {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Debt contract undefined",
			ErrorCode: "INVALID_CONTRACT",
		}
	} else {
		// TODO
		debt, err := integration.GetContractAccountingBalance(*contract.AccountingRefCode)
		if err != nil {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   "Debt contract not found",
				ErrorCode: "DEBT_NOT_FOUND",
			}
		}
		contract.Debt = debt
	}

	beneficiaryResp := model.BeneficiaryDB.QueryOne(&model.Beneficiary{
		BeneficiaryID: contract.BeneficiaryID,
	})

	if !beneficiaryResp.Ok() {
		return beneficiaryResp
	}
	beneficiary := beneficiaryResp.Data[0]

	arrContractProductCode := make([]string, 0)
	for _, productItem := range input.Items {
		arrContractProductCode = append(arrContractProductCode, productItem.ContractProductCode)
	}
	arrContractProductCode = utils.Unique(arrContractProductCode)

	contractProductsResp := model.ProductDB.Query(&model.Product{
		MainContractID: contract.ContractID,
		ComplexQuery: []*primitive.M{
			{
				"code": bson.M{
					"$in": arrContractProductCode,
				},
			},
		},
	}, 0, int64(len(arrContractProductCode)), nil)
	if !contractProductsResp.Ok() {
		return contractProductsResp
	}
	mapProducts := make(map[string]*model.Product)
	mapCheckProducts := make(map[int64]*model.Product)
	for _, item := range contractProductsResp.Data {
		mapProducts[item.Code] = item
		if mapCheckProducts[item.ProductID] != nil {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Invalid,
				Message:   "Create order do not allow duplicate product " + fmt.Sprint(item.ProductName),
				ErrorCode: "INVALID_ITEMS",
			}
		}
		mapCheckProducts[item.ProductID] = item
	}

	if len(mapProducts) != len(input.Items) {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "List items invalid",
			ErrorCode: "INVALID_ITEMS",
		}
	}

	// parse product
	arrProductUnit := make([]*pim.ProductUnit, 0)
	for _, item := range input.Items {
		if mapProducts[item.ContractProductCode] == nil {
			return utils.InvalidResponse("Product not found", "PRODUCT_NOT_FOUND")
		}

		fmt.Printf("QuantityRemain=%d, quantity=%d\n", mapProducts[item.ContractProductCode].QuantityRemain, item.Quantity)
		if mapProducts[item.ContractProductCode].QuantityRemain-item.Quantity < 0 {
			return utils.InvalidResponse(fmt.Sprintf("Product %d only allow order maximum %d", mapProducts[item.ContractProductCode].ProductID, mapProducts[item.ContractProductCode].QuantityRemain), "QUANTITY_LIMIT")
		}

		arrProductUnit = append(arrProductUnit, &pim.ProductUnit{
			ProductID:    mapProducts[item.ContractProductCode].ProductID,
			FromUnit:     mapProducts[item.ContractProductCode].Unit,
			FromQuantity: item.Quantity,
		})
	}

	arrProductUnit = utils.Unique(arrProductUnit)

	productUnits, err := pim.ExchangeUnit(arrProductUnit)
	if err != nil {
		return utils.InvalidResponse(err.Error(), "UNIT_NOT_FOUND")
	}

	mapProductUnit := make(map[int64]*pim.ProductUnitResponse)
	for _, item := range productUnits.Products {
		if item.RemainderQuantity > 0 {
			return utils.InvalidResponse(fmt.Sprintf("Product %d quy đổi %d %s, lẻ %d %s, vui lòng thay đổi số lượng đặt hàng (%s)", item.ProductID, item.ToQuantity, item.ToUnit, item.RemainderQuantity, item.RemainderUnit, item.FromUnit), "UNIT_INVALID")
		}
		mapProductUnit[item.ProductID] = item
	}

	for _, item := range input.Items {
		product := mapProducts[item.ContractProductCode]
		if product == nil {
			continue
		}

		if mapProductUnit[product.ProductID] == nil {
			return utils.InvalidResponse(fmt.Sprintf("Product %s not match unit %s", product.ProductName, product.Unit), "UNIT_INVALID")
		}

		if !mapProductUnit[product.ProductID].IsValid {
			return utils.InvalidResponse(fmt.Sprintf("Product %s not match unit %s with error %s", product.ProductName, product.Unit, mapProductUnit[product.ProductID].Description), "UNIT_INVALID")
		}
	}

	taxCode := beneficiary.TaxCode
	taxCode = strings.Replace(taxCode, " ", "", -1)
	taxCode = strings.Replace(taxCode, "\n", "", -1)
	taxCode = strings.Replace(taxCode, "\t", "", -1)
	budgetUnitcode := ""
	if contract.BudgetUnitCode != nil {
		budgetUnitcode = *contract.BudgetUnitCode
	}
	invoiceInfo := &model.InvoiceRequest{
		CompanyName:            contract.BeneficiaryName, // Note: company name
		TaxCode:                taxCode,
		CompanyAddress:         contract.Address,
		Email:                  contract.Email,
		InvoiceRequest:         utils.AnyToPointer(true),
		LegalEntityCode:        contract.LegalEntityCode,
		LegalEntityName:        contract.LegalEntityName,
		LegalEntityTaxCode:     contract.LegalEntityTaxCode,
		LegalEntityAddress:     contract.LegalEntityAddress,
		LegalEntityEmail:       contract.LegalEntityEmail,
		LegalEntityTel:         contract.LegalEntityTel,
		LegalEntityBankCode:    contract.LegalEntityBankCode,
		LegalEntityBankName:    contract.LegalEntityBankName,
		LegalEntityBankBranch:  contract.LegalEntityBankBranch,
		LegalEntityBankAccount: contract.LegalEntityBankAccount,
		ContractCode:           contract.ContractNumber,
		BudgetUnitCode:         budgetUnitcode,
	}

	if contract.AccountingRefCode != nil {
		invoiceInfo.AccountingRefCode = *contract.AccountingRefCode
	}

	versionHash, err := utils.CalculateCheckSum(invoiceInfo)
	if err == nil {
		invoiceInfo.Version = base64.URLEncoding.EncodeToString(versionHash)
	}

	orderID, orderCode, err := oms.GetOrderID()
	if err != nil {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GEN_ORDER_ERROR",
		}
	}

	newOrder := &model.Order{
		Currency:                "VND",
		AccountID:               beneficiary.AccountID,     // Account ID of Beneficiary
		ReceiverID:              beneficiary.BeneficiaryID, // Beneficiary ID
		ReceiverCode:            beneficiary.Code,          // Beneficiary Code
		ReceiverName:            input.ReceiverName,        // Beneficiary Name
		ReceiverPhoneNumber:     input.ReceiverPhoneNumber, // Receiver phone
		ReceiverShippingAddress: input.ReceiverShippingAddress,
		ReceiverEmail:           contract.Email, // Beneficiary Email
		RegionCode:              input.RegionCode,
		ProvinceCode:            input.ProvinceCode,
		DistrictCode:            input.DistrictCode,
		WardCode:                input.WardCode,
		Source:                  fmt.Sprintf("INTERNAL-%s", contract.LegalEntityBranch), // internal-tender
		ContractCode:            contract.Code,
		Note:                    input.Note, // Order note
		Invoice:                 invoiceInfo,
		PaymentMethod:           "PAYMENT_METHOD_CREDIT",    // default
		DeliveryMethod:          "DELIVERY_PLATFORM_NORMAL", // default
		DeliveryDate:            nil,                        // ??
		TotalAmount:             0,                          // Order Total Amount
		COD:                     0,                          // default
		CanExportInvoice:        &input.CanExportInvoice,
		Scope:                   scope,
	}

	if contract.AccountingRefCode != nil {
		newOrder.AccountingRefCode = *contract.AccountingRefCode
	}

	if contract.BidID != nil && *contract.BidID > 0 {
		newOrder.BidID = *contract.BidID
	}

	if actionSource != nil {
		if actionSource.Account != nil {
			newOrder.CreatedByID = actionSource.Account.AccountID
			newOrder.CreatedByName = actionSource.Account.FullName
		}

		if actionSource.Session != nil {
			newOrder.OrgID = actionSource.Session.OrgID
			newOrder.EntityID = actionSource.Session.EntityID
		}
	}

	newOrder.OrderID = orderID
	newOrder.OrderCode = orderCode

	// fulfill slice order item
	orderItems := make([]*model.OrderItem, 0)
	TRUE := true
	totalAmount := int64(0)
	for _, item := range input.Items {
		product := mapProducts[item.ContractProductCode]
		if product == nil {
			continue
		}

		if product.VAT == nil || *product.VAT <= 0 {
			return &common.APIResponse[any]{
				Status:    common.APIStatus.Error,
				Message:   fmt.Sprintf("Product %d has VAT zero", product.ProductID),
				ErrorCode: "VAT_INVALID",
			}
		}

		if len(product.ProductName) == 0 {
			lotRs := model.LotDB.QueryOne(&model.Lot{
				LotID: product.LotID,
				BidID: product.BidID,
			})
			if !lotRs.Ok() {
				return &common.APIResponse[any]{
					Status:    common.APIStatus.Error,
					Message:   fmt.Sprintf("Product %d has name invalid/empty", product.ProductID),
					ErrorCode: "PRODUCT_NAME_INVALID",
				}
			}

			// backup lot name
			lotInfo := lotRs.Data[0]
			mapProducts[item.ContractProductCode].ProductName = lotInfo.LotName
			product.ProductName = lotInfo.LotName
		}

		newItemID := model.GenOrderItemID()
		newItem := &model.OrderItem{
			OrgID:               newOrder.OrgID,
			EntityID:            newOrder.EntityID,
			CreatedByID:         newOrder.CreatedByID,
			CreatedByName:       newOrder.CreatedByName,
			ProductID:           product.ProductID,
			ProductCode:         product.ProductCode,
			ProductName:         product.ProductName,
			Volume:              product.Volume,
			ManufacturerName:    product.ManufacturerName,
			OriginName:          product.OriginName,
			Sku:                 product.Sku,
			IsImportant:         &TRUE,
			Type:                "NORMAL",
			SellerCode:          contract.LegalEntityBranch,
			SellerClass:         contract.LegalEntityBranch,
			Price:               product.Price,
			Quantity:            item.Quantity,
			TotalAmount:         product.Price * item.Quantity,
			Unit:                mapProductUnit[product.ProductID].FromUnit,
			UnitName:            mapProductUnit[product.ProductID].FromUnitName,
			SkuUnit:             mapProductUnit[product.ProductID].ToUnit,
			SkuQuantity:         mapProductUnit[product.ProductID].ToQuantity,
			ItemID:              newItemID,
			ItemCode:            model.ConvertToCode(newItemID),
			OrderID:             newOrder.OrderID,
			OrderCode:           newOrder.OrderCode,
			LotID:               product.LotID,
			ContractProductCode: product.Code,
			VAT:                 product.VAT,
			ConfigUnit:          mapProductUnit[product.ProductID],
		}

		totalAmount += newItem.TotalAmount
		orderItems = append(orderItems, newItem)
	}

	newOrder.TotalAmount = totalAmount
	newOrder.Status = string(enum.OrderState.WAIT_TO_CONFIRM)

	if contract.Debt != nil && contract.Debt.TotalBalanceTemporary-newOrder.TotalAmount < 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Error,
			Message:   "The order exceeds the allowed debt limit, please contact the admin.",
			ErrorCode: "DEBT_OVER",
		}
	}

	createResult := model.OrderDB.Create(newOrder)
	if !createResult.Ok() {
		return createResult
	}

	for _, orderItem := range orderItems {
		model.ProductDB.UpdateOneWithOperator(&model.Product{
			Code:           orderItem.ContractProductCode,
			MainContractID: contract.ContractID,
		}, bson.M{
			"$inc": bson.M{
				"quantity_remain":     -orderItem.Quantity,
				"quantity_processing": orderItem.Quantity,
			},
		})
	}

	createItemResult := model.OrderItemDB.CreateManyWithCtx(context.TODO(), orderItems)
	if !createItemResult.Ok() {
		return createItemResult
	}

	return applyOrder(enum.CREATE_ORDER, nil, createResult)
}

// QueryOrderBidList ...
func QueryOrderBidList(query *request.OrderBidQuery, search string, offset, limit int64, queryOptions request.QueryOption, actionSource *request.ActionSource) common.Response {
	if len(query.ContractCode) > 0 {
		queryContract := &model.Contract{
			Code: query.ContractCode,
		}

		// permission check
		scope := utils.GenQueryScope(*actionSource)
		if scope != nil {
			queryContract.ComplexQuery = append(queryContract.ComplexQuery, &bson.M{
				"scope": bson.M{"$in": scope},
			})
		}

		respContract := model.ContractDB.QueryOne(queryContract)
		if !respContract.Ok() {
			return utils.InvalidResponse("Not found any matched contract", "CONTRACT_NOT_FOUND")
		}

		query.ContractCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"contract_code": respContract.Data[0].Code,
		})

	}

	// permission check
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	resp := model.OrderDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: -1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.OrderDB.Count(query).Total
	}
	return resp
}

// UpdateOrderStatus ...
func UpdateOrderStatus(input *request.UpdateOrderStatusRequest, actionSource *request.ActionSource) common.Response {
	if es := input.Validate(); !es.Ok() {
		return es
	}

	orderQuery := &model.Order{OrderID: input.OrderID}

	// Check scope for the action source
	scope := utils.GenQueryScope(*actionSource)
	if scope == nil { // allow admin
	} else { // empty array => invalid
		orderQuery.ComplexQuery = append(orderQuery.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	orderResp := model.OrderDB.QueryOne(orderQuery)

	if !orderResp.Ok() {
		return utils.NotfoundDataResponse[any]("Not found any matched order", "ORDER_NOT_FOUND")
	}

	orderInfo := orderResp.Data[0]

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: orderInfo.ContractCode,
	})

	if !contractResp.Ok() {
		return utils.InvalidResponse("Not found any matched contract", "CONTRACT_NOT_FOUND")
	}

	contract := contractResp.Data[0]

	err := ValidateOrderContractScope(*actionSource, contract)
	if err != nil {
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	if orderInfo.Status == input.Status {
		return orderResp
	}

	updater := &model.Order{}

	if input.Status != "" {
		updater.Status = input.Status

		if input.Status == string(enum.OrderState.CONFIRMED) {
			updater.ConfirmationDate = utils.AnyToPointer(time.Now())
		}
	}

	resp := model.OrderDB.UpdateOne(model.Order{
		OrderID: orderInfo.OrderID,
		Status:  orderInfo.Status,
	}, updater)

	if !resp.Ok() {
		return resp
	}

	if orderInfo.Status == string(enum.OrderState.WAIT_TO_CONFIRM) && updater.Status == string(enum.OrderState.CONFIRMED) {
		_ = event.OnOrderConfirmed(resp.Data[0])
	}

	return resp
}

// GetOrderInfo ...
func GetOrderInfo(orderCode string, option *request.GetOrderInfoQuery, actionSource *request.ActionSource) common.Response {
	if len(orderCode) == 0 {
		return utils.InvalidResponse("Invalid order code", "INVALID_ORDER_CODE")
	}

	orderQuery := &model.Order{OrderCode: orderCode}

	// permission check
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		orderQuery.ComplexQuery = append(orderQuery.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	orderResp := model.OrderDB.QueryOne(orderQuery)
	if !orderResp.Ok() {
		return orderResp
	}

	order := orderResp.Data[0]
	orderItemsResp := model.OrderItemDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
	}, 0, 1000, nil)

	if orderItemsResp.Ok() {
		order.Items = orderItemsResp.Data
	}

	if option != nil {
		if option.Contract {
			contractResult := model.ContractDB.QueryOne(&model.Contract{
				Code: order.ContractCode,
			})
			if contractResult.Ok() {
				order.ContractInfo = contractResult.Data[0]
				if option.Bid && order.ContractInfo.BidID != nil {
					bidResult := model.BidDB.QueryOne(&model.Bid{
						BidID: *order.ContractInfo.BidID,
					})
					if bidResult.Ok() {
						order.ContractInfo.BidInfo = bidResult.Data[0]
					}
				}
			}
		}
	}

	return &common.APIResponse[*model.Order]{
		Status:  common.APIStatus.Ok,
		Message: orderResp.Message,
		Data: []*model.Order{
			order,
		},
	}
}

// UpdateSaleOrderFulfillment ...
func UpdateSaleOrderFulfillment(input *request.UpdateSaleOrderFulfillmentRequest, actionSource *request.ActionSource) common.Response {

	orderQuery := &model.Order{OrderID: input.OrderID}

	// permission check
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		orderQuery.ComplexQuery = append(orderQuery.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	orderResult := model.OrderDB.QueryOne(orderQuery)

	if !orderResult.Ok() {
		return orderResult
	}

	order := orderResult.Data[0]

	var beneficiaryInfo *model.Beneficiary
	beneficiaryRs := model.BeneficiaryDB.QueryOne(&model.Beneficiary{
		Code: order.ReceiverCode,
	})
	if beneficiaryRs.Ok() {
		beneficiaryInfo = beneficiaryRs.Data[0]
	}

	updater := &model.Order{}
	if input.SaleOrderCode != nil && len(*input.SaleOrderCode) > 0 {
		updater.SaleOrderCode = *input.SaleOrderCode
	}

	if input.SaleOrderCreatedTime != nil {
		updater.SaleOrderCreatedTime = input.SaleOrderCreatedTime
	}

	if input.Status != nil && len(*input.Status) > 0 {
		updater.Status = *input.Status
	}

	if input.InvoiceNo != nil && len(*input.InvoiceNo) > 0 {
		updater.InvoiceNo = input.InvoiceNo
	}

	if input.DeliveredTime != nil {
		updater.DeliveredTime = input.DeliveredTime
	}

	if input.OutboundDate != nil {
		updater.OutboundDate = input.OutboundDate
	}

	if input.CompletedTime != nil {
		updater.CompletedTime = input.CompletedTime
	}

	if input.CompletedDebtTime != nil {
		updater.CompletedDebtTime = input.CompletedDebtTime
	}

	if input.InvoiceExchangeNo != nil {
		updater.InvoiceExchangeNo = input.InvoiceExchangeNo
	}

	if input.ActualTotalPrice != nil && *input.ActualTotalPrice > 0 {
		updater.ActualAmount = utils.AnyToPointer(int64(*input.ActualTotalPrice))
	}

	if updater.Status == string(enum.OrderState.CANCELLED) {
		updater.Status = string(enum.OrderState.DELETING)
	}

	if input.CancelTime != nil {
		updater.CancelTime = input.CancelTime
	}

	if input.PaidAmount != nil && *input.PaidAmount > 0 {
		updater.PaidAmount = input.PaidAmount
	}

	if input.PaidTime != nil {
		updater.PaidTime = input.PaidTime
	}

	if input.PaymentStatus != nil && len(*input.PaymentStatus) > 0 {
		updater.PaymentStatus = input.PaymentStatus
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: order.ContractCode,
	})

	if !contractResp.Ok() {
		return contractResp
	}

	contract := contractResp.Data[0]

	err := ValidateOrderContractScope(*actionSource, contract)
	if err != nil {
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	updaterRs := model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID}, updater)
	if updaterRs.Ok() {
		orderInfo := updaterRs.Data[0]

		//
		if updater.InvoiceNo != nil && beneficiaryInfo != nil &&
			(beneficiaryInfo.IsVerifyTax == nil || !*beneficiaryInfo.IsVerifyTax) {
			_ = model.BeneficiaryDB.UpdateOne(&model.Beneficiary{
				Code: orderInfo.ReceiverCode,
			}, &model.Beneficiary{
				IsVerifyTax: utils.AnyToPointer(true),
			})
		}

		// cancel order
		if (order.Status == string(enum.OrderState.DELIVERED) || order.Status == string(enum.OrderState.DELIVERING)) && updater.Status == string(enum.OrderState.DELETING) {
			es := revertQuantityProduct(orderInfo)
			if !es.Ok() {
				return utils.ErrorResponse(es.Message, es.ErrorCode)
			}

			updateRs := model.OrderDB.UpdateOne(&model.Order{
				OrderID: orderInfo.OrderID,
				Status:  string(enum.OrderState.DELETING),
			}, &model.Order{
				Status: string(enum.OrderState.CANCELLED),
			})

			return updateRs
		}

		// complete order
		if orderInfo.Status == "COMPLETED" && order.PaymentStatus == nil &&
			order.PaidAmount == nil && orderInfo.PaymentStatus != nil &&
			*orderInfo.PaymentStatus == "COMPLETED" &&
			orderInfo.PaidAmount != nil && *orderInfo.PaidAmount > 0 {
			contractRs := model.ContractDB.QueryOne(&model.Contract{
				Code: orderInfo.ContractCode,
			})
			if contractRs.Ok() {
				_ = model.ContractDB.UpdateOneWithOperator(&model.Contract{
					ID: contractRs.Data[0].ID,
				}, bson.M{
					"$inc": bson.M{
						"current_amount": *orderInfo.PaidAmount,
					},
				})
			}
		}
	}

	if len(input.Items) > 0 {
		mapItems := make(map[string]*request.UpdateItemMsg)
		for _, item := range input.Items {
			mapItems[item.Sku] = item
		}
		orderItemsRs := model.OrderItemDB.Query(&model.OrderItem{
			OrderID: order.OrderID,
		}, 0, 1000, nil)
		if orderItemsRs.Ok() {
			orderItems := orderItemsRs.Data
			for _, item := range orderItems {
				if itemInfo := mapItems[fmt.Sprintf("%s.%s", item.SellerCode, item.ProductCode)]; itemInfo != nil {
					itemUpdater := model.OrderItem{}
					if itemInfo.CompletedQuantity != nil {
						itemUpdater.CompletedQuantity = itemInfo.CompletedQuantity
						itemUpdater.ActualPrice = utils.AnyToPointer(*itemInfo.CompletedQuantity * int64(item.Price))
					}

					if len(itemInfo.OutboundInfos) > 0 {
						itemUpdater.CompletedInfos = itemInfo.OutboundInfos
					}

					if len(itemInfo.ReturnInfos) > 0 {
						itemUpdater.ReturnInfos = itemInfo.ReturnInfos
					}

					updaterRs := model.OrderItemDB.UpdateOne(&model.OrderItem{
						ID: item.ID,
					}, itemUpdater)
					if updaterRs.Ok() {
						nestOrderItem := updaterRs.Data[0]
						if len(item.CompletedInfos) == 0 && len(itemUpdater.CompletedInfos) > 0 &&
							item.Quantity > 0 && itemUpdater.CompletedQuantity != nil {
							contractResp := model.ContractDB.QueryOne(&model.Contract{
								Code: order.ContractCode,
							})
							if contractResp.Ok() {
								contract := contractResp.Data[0]
								qtyRemain := item.Quantity - *itemUpdater.CompletedQuantity

								if qtyRemain >= 0 {
									_ = model.ProductDB.UpdateOneWithOperator(&model.Product{
										Code:           nestOrderItem.ContractProductCode,
										MainContractID: contract.ContractID,
										ComplexQuery: []*primitive.M{
											{
												"quantity_processing": bson.M{
													"$gte": nestOrderItem.Quantity,
												},
											},
										},
									}, bson.M{
										"$inc": bson.M{
											"quantity_remain":     qtyRemain, // con lai
											"quantity_processing": -nestOrderItem.Quantity,
											"quantity_sold":       *itemUpdater.CompletedQuantity, // completed
										},
									})
								}
							}
						}
					}
				}
			}
		}
	}

	return updaterRs
}

func CancelOrder(input *oms.CancelOrderRequest, account *request.ActionSource) common.Response {
	if len(input.OrderCode) <= 0 {
		return utils.InvalidResponse("Invalid order code", "INVALID_ORDER_CODE")
	}

	if account == nil || account.Account == nil {
		return utils.InvalidDataResponse[any]("PERMISSION_DENIED", "Do not have permission to perform this operation")
	}

	input.AccountID = account.Account.AccountID
	input.AccountName = account.Account.Username

	orderResult := model.OrderDB.QueryOne(&model.Order{
		OrderCode: input.OrderCode,
	})

	if !orderResult.Ok() {
		return orderResult
	}

	orderInfo := orderResult.Data[0]

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: orderInfo.ContractCode,
	})

	if !contractResp.Ok() {
		return contractResp
	}

	contract := contractResp.Data[0]

	err := ValidateOrderContractScope(*account, contract)
	if err != nil {
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	// skip this trigger if order was cancelled before
	if orderInfo.Status == string(enum.OrderState.CANCELLED) || orderInfo.Status == string(enum.OrderState.DELETING) {
		return utils.InvalidDataResponse[any]("ORDER_CANCELLED", "Order cancelled")
	}

	if orderInfo.Status == string(enum.OrderState.WAIT_TO_CONFIRM) {
		// force update order with status CANCELLED, then push a event to reset balance debt
		updateRs := model.OrderDB.UpdateOne(&model.Order{
			OrderID: orderInfo.OrderID,
			Status:  orderInfo.Status,
		}, &model.Order{
			Status:        string(enum.OrderState.DELETING),
			UpdatedByName: input.AccountName,
			UpdatedByID:   input.AccountID,
		})
		if !updateRs.Ok() {
			return utils.ErrorResponse(updateRs.Message, updateRs.ErrorCode)
		}

		es := revertQuantityProduct(orderInfo)
		if !es.Ok() {
			return utils.ErrorResponse(es.Message, es.ErrorCode)
		}

		updateRs = model.OrderDB.UpdateOne(&model.Order{
			OrderID: orderInfo.OrderID,
			Status:  string(enum.OrderState.DELETING),
		}, &model.Order{
			Status:        string(enum.OrderState.CANCELLED),
			UpdatedByName: input.AccountName,
			UpdatedByID:   input.AccountID,
		})

		if updateRs.Ok() {
			return applyOrder(enum.CANCEL_ORDER, nil, updateRs)
		}
		return updateRs
	} else if (orderInfo.Status == string(enum.OrderState.PROCESSING) &&
		len(orderInfo.SaleOrderCode) > 0) || (orderInfo.Status == string(enum.OrderState.CONFIRMED) && len(orderInfo.SaleOrderCode) == 0) {
		input.OrderID = orderInfo.OrderID
		err := oms.RequestCancelOrder(input)
		if err != nil {
			return utils.ErrorResponse(err.Error(), "ACTION_FAILED")
		}

		updateRs := model.OrderDB.UpdateOne(&model.Order{
			OrderID: orderInfo.OrderID,
			Status:  orderInfo.Status,
		}, &model.Order{
			Status:        string(enum.OrderState.DELETING),
			UpdatedByName: input.AccountName,
			UpdatedByID:   input.AccountID,
		})
		if !updateRs.Ok() {
			return utils.ErrorResponse(updateRs.Message, updateRs.ErrorCode)
		}

		es := revertQuantityProduct(orderInfo)
		if !es.Ok() {
			return utils.ErrorResponse(es.Message, es.ErrorCode)
		}

		updateRs = model.OrderDB.UpdateOne(&model.Order{
			OrderID: orderInfo.OrderID,
			Status:  string(enum.OrderState.DELETING),
		}, &model.Order{
			Status:        string(enum.OrderState.CANCELLED),
			UpdatedByName: input.AccountName,
			UpdatedByID:   input.AccountID,
		})
		return updateRs
	}
	return utils.InvalidDataResponse[any]("ACTION_NOT_ALLOW", "This order can not cancel")
}

func RequestExportInvoiceExchange(orderCode string, account *request.ActionSource) common.Response {
	if len(orderCode) <= 0 {
		return utils.InvalidResponse("Invalid order code", "INVALID_ORDER_CODE")
	}

	if account.Account == nil || account.Account.AccountID <= 0 {
		return utils.InvalidResponse("Invalid account", "INVALID_ACCOUNT")
	}

	orderResult := model.OrderDB.QueryOne(&model.Order{
		OrderCode: orderCode,
	})

	if !orderResult.Ok() {
		return orderResult
	}

	orderInfo := orderResult.Data[0]

	// skip this trigger if order was cancelled before
	if orderInfo.Status != string(enum.OrderState.DELIVERING) &&
		orderInfo.Status != string(enum.OrderState.DELIVERED) &&
		orderInfo.Status != string(enum.OrderState.COMPLETED) {
		return utils.InvalidDataResponse[any]("ORDER_INVALID", "Order invalid status")
	}

	if orderInfo.InvoiceNo == nil || len(*orderInfo.InvoiceNo) == 0 {
		return utils.InvalidDataResponse[any]("INVOICE_NOT_EXISTED", "Invoice not found")
	}

	if orderInfo.AskExportInvoice != nil {
		return utils.InvalidDataResponse[any]("INVOICE_EXPORTING", "Please wait to get file invoice")
	}

	t := time.Now()
	_, err := oms.RequestInvoiceExchange(&oms.InvoiceExchangeRequest{
		OrderID: orderInfo.OrderID,
	})
	if err != nil {
		return utils.ErrorResponse("Request export invoice can not accept", "ACTION_FAILED")
	}

	updaterRs := model.OrderDB.UpdateOne(&model.Order{
		OrderID: orderInfo.OrderID,
	}, &model.Order{
		AskExportInvoice:   &t,
		AskExportInvoiceBy: &account.Account.AccountID,
	})

	return updaterRs
}

// func validate create product
func ValidateOrderContractScope(actionSource request.ActionSource, contract *model.Contract) error {
	if contract == nil || contract.LegalEntityCode == "" {
		return errors.New("contract legal entity is nil")
	}
	return utils.ValidateContractScope(actionSource, contract.LegalEntityCode)
}
