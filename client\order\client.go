package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout    = 60 * time.Second
	pathGetListOrders = "/marketplace/order/v2/order/list"
)

type client struct {
	host          string
	defaultClient *sdk_client.RestClient
	headers       map[string]string
}

var orderProxyClient *client

// InitOrderProxyClient ...
func InitOrderProxyClient(_ *mongo.Database) {
	orderProxyClient = &client{
		host: conf.Config.BuymedVNClient.Host,
		headers: map[string]string{
			"Authorization": conf.Config.BuymedVNClient.Authorization,
		},
		defaultClient: sdk_client.NewRESTClient(conf.Config.BuymedVNClient.Host, "", defaultTimeout, 0, 0),
	}
}

type orderFilter struct {
	Source     string `json:"source"`
	CustomerId int64  `json:"customerId"`
	Status     string `json:"status,omitempty"`
	OrderID    int64  `json:"orderId,omitempty"`
}

// GetListOrders ...
func GetListOrders(status string, orderID, offset, limit int64) ([]*Order, int64, error) {
	if conf.Config.CustomerID <= 0 {
		return nil, 0, errors.New("error: customer not found")
	}

	payload := &orderFilter{
		CustomerId: conf.Config.CustomerID,
		Source:     "brand-portal",
	}

	if len(status) > 0 {
		payload.Status = status
	}

	if orderID > 0 {
		payload.OrderID = orderID
	}

	q, _ := json.Marshal(payload)
	params := map[string]string{
		"q":        string(q),
		"offset":   fmt.Sprint(offset),
		"limit":    fmt.Sprint(limit),
		"getTotal": "true",
	}
	resp, err := orderProxyClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Get, orderProxyClient.headers, params, nil, pathGetListOrders)
	if err != nil {
		return nil, 0, err
	}

	if resp.Code != http.StatusOK {
		return nil, 0, errors.New("error: fail to call get order")
	}

	var data *OrderMarketplaceResponse
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return nil, 0, errors.New("error: fail to read data order")
	}

	if len(data.Data) == 0 {
		return nil, 0, errors.New("error: order not found")
	}

	return data.Data, data.Total, nil
}
