package request

import "go.mongodb.org/mongo-driver/bson"

type CreateProductRequest struct {
	LotID           int64  `json:"lotID,omitempty"`
	ProductID       int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ContractID      int64  `json:"contractID,omitempty" bson:"contract_id,omitempty"`
	Price           int64  `json:"price,omitempty" bson:"price,omitempty"`
	PriceUnit       string `json:"priceUnit,omitempty" bson:"price_unit,omitempty"` // VND
	Amount          int64  `json:"amount,omitempty" bson:"amount,omitempty"`
	Quantity        int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	GuaranteeAmount int64  `json:"guaranteeAmount,omitempty" bson:"guarantee_amount,omitempty"`
	GroupMedicine   int64  `json:"groupMedicine,omitempty" bson:"group_medicine,omitempty"`
	DosageForm      string `json:"dosageForm,omitempty" bson:"dosage_form,omitempty"`
	Packaging       string `json:"packaging,omitempty" bson:"packaging,omitempty"`
	Unit            string `json:"unit,omitempty" bson:"unit,omitempty"`
	RegistrationNo  string `json:"registrationNo,omitempty" bson:"registration_no,omitempty"`
	Vendor          string `json:"vendor,omitempty" bson:"vendor,omitempty"`
	Manufacturer    string `json:"manufacturer,omitempty" bson:"manufacturer,omitempty"`
	Origin          string `json:"origin,omitempty" bson:"origin,omitempty"`
}

// UpdateProductRequest ...
type UpdateProductRequest struct {
	Code           string  `json:"code"`
	ProductID      *int64  `json:"productID"`
	Quantity       *int64  `json:"quantity"`
	GroupMedicine  *int64  `json:"groupMedicine"`
	RegistrationNo *string `json:"registrationNo"`
	Price          int64   `json:"price,omitempty"`
}

// ProductQuery ...
type ProductQuery struct {
	BidID        int64     `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	ContractNo   string    `json:"contractNo,omitempty" bson:"-"`
	ContractCode string    `json:"contractCode,omitempty" bson:"-"`
	ContractType string    `json:"contractType,omitempty" bson:"-"`
	Status       string    `json:"status,omitempty" bson:"status,omitempty"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// ProductFuzzySearchQuery ...
type ProductFuzzySearchQuery struct {
	Text       string  `json:"text"`
	IsHaveSku  bool    `json:"isHaveSku"`
	ProductIDs []int64 `json:"productIDs"`
	Branch     string  `json:"branch"`
}
