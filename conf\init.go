package conf

import (
	"fmt"
	"os"

	"gitlab.buymed.tech/sdk/golang/configuration"
)

type configModel struct {
	AppName  string
	Env      string
	Protocol string
	Version  string

	CustomerID int64

	MainDBConf configuration.Database
	LogDBConf  configuration.Database
	JobDBConf  configuration.Database

	BuymedVNClient       configuration.Service
	BuymedPlatformClient configuration.Service

	// accouting
	TemplateCode string
}

// Config main config object
var Config *configModel
var IS_DEBUG = false

// LoadAppConfig is func load config from environment
func LoadAppConfig(namespace, serviceName string) {

	fmt.Println("Config loading ...")
	defer fmt.Println("Config loaded.")

	// General
	env := configuration.Env()
	version := configuration.Version()
	protocol := configuration.Protocol()
	if protocol == "" {
		protocol = "THRIFT"
	}

	Config = &configModel{
		Env:      env,
		Protocol: protocol,
		Version:  version,

		AppName: namespace + "/" + serviceName,

		// DB
		MainDBConf: initMainDBConfig(configuration.Get("db").ToDatabaseConfig(), namespace, serviceName, env),
		LogDBConf:  initLogDBConfig(configuration.Get("logDB").ToDatabaseConfig(), namespace, serviceName, env),
		JobDBConf:  initJobDBConfig(configuration.Get("jobDB").ToDatabaseConfig(), namespace, serviceName, env),

		// Service
		// local uses 'thuocsi-vn-client'
		BuymedVNClient:       configuration.Get("buymed-vn-client").ToServiceConfig(),
		BuymedPlatformClient: configuration.Get("buymed-com-client").ToServiceConfig(),
	}

	switch env {
	case "stg":
		{
			Config.CustomerID = 1390
			Config.TemplateCode = "CT5"
			break
		}
	case "uat":
		{
			Config.CustomerID = 375375
			Config.TemplateCode = "DT1"
			break
		}
	case "prd":
		{
			Config.CustomerID = 375375
			Config.TemplateCode = "DT1"
			break
		}
	}

	// Determine if debugging is enabled
	IS_DEBUG = os.Getenv("debug-local") == "true"
}

func initMainDBConfig(_config configuration.Database, namespace, serviceName, env string) configuration.Database {

	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}

	_config.DatabaseName = fmt.Sprintf("%s_%s_%s", namespace, env, serviceName) // platform_human-resource_prd_employee

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func initJobDBConfig(_config configuration.Database, namespace, serviceName, env string) configuration.Database {

	_config.DatabaseName = fmt.Sprintf("%s_%s_%s_job", namespace, env, serviceName) // platform_human-resource_prd_employee_log

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func initLogDBConfig(_config configuration.Database, namespace, serviceName, env string) configuration.Database {

	_config.DatabaseName = fmt.Sprintf("%s_%s_%s_log", namespace, env, serviceName) // platform_human-resource_prd_employee_job

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}
