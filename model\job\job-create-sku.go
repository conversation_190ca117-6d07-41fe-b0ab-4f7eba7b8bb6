package job

import (
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/product"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue/basic_queue"
	"go.mongodb.org/mongo-driver/mongo"
)

type CreateSkuMsg struct {
	Sku     *product.SkuMainRequest
	SkuItem *product.SkuItemRequest
}

var JobCreateSkuToMarketplace *basic_queue.Queue[*CreateSkuMsg]

func InitJobCreateSkuToMarketplace(session *mongo.Database) {

	JobCreateSkuToMarketplace = basic_queue.New[*CreateSkuMsg](
		"tender_create_sku_to_marketplace", &db_queue.Configuration{
			CurVersionTimeoutS:  20 * 60, // 20 min
			OldVersionTimeoutS:  10 * 60, // 10 min
			LogSize:             5,
			MaximumWaitToRetryS: 3, // 3 secs
			ChannelCount:        4,
			ConsumedExpiredTime: time.Duration(7*24) * time.Hour,
			FailThreshold:       3,
			UniqueItem:          true,
		},
	)

	JobCreateSkuToMarketplace.Init(session)
}
