package action

import (
	"fmt"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
)

func ReviewProductUnit(input *request.ReviewProductUnitRequest) common.Response {
	arrProductUnit := make([]*pim.ProductUnit, 0)
	for _, item := range input.Products {
		arrProductUnit = append(arrProductUnit, &pim.ProductUnit{
			ProductID:    item.ProductID,
			FromUnit:     item.FromUnit,
			FromQuantity: item.FromQuantity,
		})
	}
	arrProductUnit = utils.Unique(arrProductUnit)

	productUnits, err := pim.ExchangeUnit(arrProductUnit)
	if err != nil {
		return utils.InvalidResponse(err.<PERSON>rror(), "UNIT_NOT_FOUND")
	}

	mapProductUnit := make(map[int64]*pim.ProductUnitResponse)
	for _, item := range productUnits.Products {
		mapProductUnit[item.ProductID] = item
	}

	resp := make([]*request.ReviewProductUnit, 0)
	for _, item := range input.Products {
		if mapProductUnit[item.ProductID] == nil || len(mapProductUnit[item.ProductID].Rate) != 2 {
			return utils.InvalidResponse(fmt.Sprintf("Product %d not match unit", item.ProductID), "UNIT_NOT_FOUND")
		}
		rates := mapProductUnit[item.ProductID].Rate

		// set price for  1 quantity
		retailPriceTmp := item.Price * item.FromQuantity * rates[1] / rates[0]

		resp = append(resp, &request.ReviewProductUnit{
			ProductID:         item.ProductID,
			FromUnit:          item.FromUnit,
			FromQuantity:      item.FromQuantity,
			FromUnitName:      mapProductUnit[item.ProductID].FromUnitName,
			ToUnit:            mapProductUnit[item.ProductID].ToUnit,
			ToUnitName:        mapProductUnit[item.ProductID].ToUnitName,
			ToQuantity:        mapProductUnit[item.ProductID].ToQuantity,
			Price:             retailPriceTmp,
			RemainderQuantity: mapProductUnit[item.ProductID].RemainderQuantity,
			RemainderUnit:     mapProductUnit[item.ProductID].RemainderUnit,
			Rate:              mapProductUnit[item.ProductID].Rate,
		})
	}

	if len(resp) == 0 {
		return utils.InvalidResponse("Product not found", "PRODUCT_NOT_FOUND")
	}

	return &common.APIResponse[*request.ReviewProductUnit]{
		Status: common.APIStatus.Ok,
		Data:   resp,
	}
}

func QueryProductUnit(query *request.ProductUnitQuery, search string, offset, limit int64, queryOptions request.QueryOption) common.Response {
	productUnits, err := pim.GetConversionUnit(query.ProductIDs, limit)
	if err != nil {
		return utils.InvalidResponse(err.Error(), "UNIT_NOT_FOUND")
	}
	return &common.APIResponse[*pim.ConversionUnit]{
		Status: common.APIStatus.Ok,
		Data:   productUnits,
	}
}
