package utils

import (
	"encoding/json"
	"errors"
	"regexp"
	"strconv"
	"strings"
	"unicode"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

const (
	CombinedSession = "combined"
	SingleSession   = "single"
)

func GetActionSource(req server.APIRequest) *request.ActionSource {

	// debug emulator
	if conf.IS_DEBUG {
		return &request.ActionSource{
			Account: &request.Account{
				AccountID: 1006718,
				Type:      "USER",
			},
			Session: &request.Session{
				OrgID:    2,
				EntityID: 4301,
			},
			UserRoles: []*request.UserRole{
				{
					OrgID:    2,
					EntityID: 4301,
					RoleCode: "FIN",
					Entity: request.Entity{
						Attributes: map[string]interface{}{
							"scope": []string{
								"MEDX",
							},
						},
					},
				},
			},
		}
	}

	var source []*request.ActionSource
	sourceAttr := req.GetAttribute("X-Source")
	if sourceAttr != nil {
		source = sourceAttr.([]*request.ActionSource)
		if source == nil || source[0] == nil || source[0].Account == nil || source[0].Account.AccountID <= 0 {
			return nil
		}
		return source[0]
	}
	sourceStr := req.GetHeader("X-Source")
	if sourceStr == "" {
		return nil
	}

	var sourceHeader *request.ActionSource
	err := json.Unmarshal([]byte(sourceStr), &sourceHeader)
	if err != nil || sourceHeader == nil || sourceHeader.Account == nil {
		return nil
	}

	return sourceHeader
}

var mapVNICode = map[string]string{
	"a": "[à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ]",
	"ê": "[è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ]",
	"i": "[ì|í|ị|ỉ|ĩ]",
	"o": "[ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ]",
	"u": "[ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ]",
	"y": "[ỳ|ý|ỵ|ỷ|ỹ]",
	"d": "[đ]",
	"-": "[\\_|\\-|\\t|\\s]+",
}

func formatVNCode(str string) string {
	for key, val := range mapVNICode {
		m := regexp.MustCompile(val)
		str = m.ReplaceAllString(str, key)
	}
	return str
}

// NormalizeString ...
func NormalizeString(val string) string {
	//nolint
	tran := transform.Chain(norm.NFD, transform.RemoveFunc(isMn), norm.NFC)
	normStr, _, _ := transform.String(tran, strings.ToLower(val))
	normStr = formatVNCode(normStr)
	normStr = strings.Replace(normStr, " ", "-", -1)
	normStr = strings.Replace(normStr, ",", "", -1)
	normStr = strings.Replace(normStr, ";", "", -1)
	normStr = strings.Replace(normStr, "(", "", -1)
	normStr = strings.Replace(normStr, ")", "", -1)
	normStr = strings.Replace(normStr, "/", "", -1)
	normStr = strings.Replace(normStr, "&", "", -1)
	normStr = strings.Replace(normStr, "%", "", -1)

	// Remove - at the end and start of string
	m := regexp.MustCompile("^-+|-+$")
	normStr = m.ReplaceAllString(normStr, "")

	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(normStr, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		normStr = strings.ReplaceAll(normStr, v, ``)
	}

	return strings.Replace(normStr, "--", "-", -1)
}

func isMn(r rune) bool {
	return unicode.Is(unicode.Mn, r)
}

func ParserQ(q string) string {
	return strings.Replace(NormalizeString(q), " ", "-", -1)
}

func ParseInt64(text string, defaultValue int64) int64 {
	if text == "" {
		return defaultValue
	}

	num, err := strconv.ParseInt(text, 10, 64)
	if err != nil {
		return defaultValue
	}
	return num
}

func ParseInt(text string, defaultValue int) int {
	if text == "" {
		return defaultValue
	}

	num, err := strconv.Atoi(text)
	if err != nil {
		return defaultValue
	}
	return num
}

func GenQueryScope(input request.ActionSource) *[]string {
	var scope = []string{}

	if input.Account.Type == "SYSTEM" && input.Session != nil {
		// scope = append(scope, strings.Split(input.Session.Scope, ",")...)

		// by pass all
		return nil
	}

	if input.Account.Type == "USER" {
		for i := range input.UserRoles {
			role := input.UserRoles[i]
			if role.RoleCode == "ADMIN" {
				// by pass all
				return nil
			}
			if role.Entity.Attributes == nil {
				continue
			}
			scopeAny, ok := role.Entity.Attributes["scope"]
			if !ok {
				continue
			}
			scopeArr := []string{}
			b, _ := json.Marshal(scopeAny)
			json.Unmarshal(b, &scopeArr)

			scope = append(scope, scopeArr...)
		}
	}

	scope = UniqueStringSlice(scope)
	return &scope
}

// common validation function
func ValidateContractScope(actionSource request.ActionSource, legalEntityCode string) error {
	scope := GenQueryScope(actionSource)
	if scope == nil { // bypass admin
		return nil
	}

	if !IsContains(*scope, legalEntityCode) {
		return errors.New("you can't action with this legal entity")
	}
	return nil
}
