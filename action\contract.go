package action

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/product"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/enum"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateContract ...
func CreateContract(input *request.CreateContractRequest, actionSource *request.ActionSource) common.Response {
	if es := input.Validate(); !es.Ok() {
		return es
	}

	// lot array
	lotIDs := make([]int64, 0)
	mapLots := make(map[int64]*model.Lot)

	// product mapping
	products := make([]*model.Product, 0)

	input.TaxCode = strings.TrimSpace(input.TaxCode)
	input.TaxCode = strings.Replace(input.TaxCode, " ", "", -1)
	input.TaxCode = strings.Replace(input.TaxCode, `\t`, ``, -1)
	input.TaxCode = strings.Replace(input.TaxCode, `\n`, ``, -1)

	input.LegalEntityTaxCode = strings.TrimSpace(input.LegalEntityTaxCode)
	input.LegalEntityTaxCode = strings.Replace(input.LegalEntityTaxCode, `\t`, ``, -1)
	input.LegalEntityTaxCode = strings.Replace(input.LegalEntityTaxCode, `\n`, ``, -1)

	input.TaxCode = utils.NormalizeTaxCode(input.TaxCode)
	err := CheckTax(input.TaxCode)
	if err != nil {
		return utils.InvalidResponse(err.Error(), "INVALID_TAX_CODE")
	}

	// permission check
	scope := []string{input.LegalEntityCode}
	err = ValidateCreateContractScope(*actionSource, input)
	if err != nil { // allow admin
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   "You can't create contract with this legal entity",
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}
	if len(input.BudgetUnitCode) != 0 && len(input.BudgetUnitCode) != 7 {
		return utils.InvalidResponse("Mã ngân sách phải là 7 kí tự", "INVALID_BUDGET_UNIT_CODE")
	}

	contractInfo := &model.Contract{
		Name:              input.Name,
		ContractNumber:    input.ContractNumber,
		ContractType:      input.ContractType,
		DebtLimit:         input.DebtLimit,
		PaymentTerm:       input.PaymentTerm,
		BeneficiaryName:   input.BeneficiaryName,
		DecisionNo:        input.DecisionNo,
		TaxCode:           input.TaxCode,
		PhoneNumber:       input.PhoneNumber,
		TermOfPayment:     input.TermOfPayment,
		Email:             input.Email,
		Address:           input.Address,
		SigningDate:       input.SigningDate,
		ExpireDate:        input.ExpireDate,
		Content:           input.Content,
		Note:              input.Note,
		Attachments:       input.Attachments,
		ExtendAttachments: input.ExtendAttachments,
		NumOfProduct:      int64(len(input.Products)),

		// legal
		LegalEntityCode:        input.LegalEntityCode,
		LegalEntityName:        input.LegalEntityName,
		LegalEntityTaxCode:     input.LegalEntityTaxCode,
		LegalEntityAddress:     input.LegalEntityAddress,
		LegalEntityEmail:       input.LegalEntityEmail,
		LegalEntityTel:         input.LegalEntityTel,
		LegalEntityBankCode:    input.LegalEntityBank.BankCode,
		LegalEntityBankName:    input.LegalEntityBank.BankName,
		LegalEntityBankBranch:  input.LegalEntityBank.BankBranch,
		LegalEntityBankAccount: input.LegalEntityBank.AccountNumber,
		LegalEntityBranch:      input.LegalEntityBranch,
		Scope:                  scope,
		BudgetUnitCode:         &input.BudgetUnitCode,
	}

	if input.BidID != nil {
		for _, item := range input.Products {
			if item.LotID > 0 {
				lotIDs = append(lotIDs, item.LotID)
			}
		}
		lotIDs = utils.Unique(lotIDs)
		if len(lotIDs) == 0 {
			return utils.InvalidResponse("Invalid Lots", "LOTS_REQUIRED")
		}
		bidResp := model.BidDB.QueryOne(&model.Bid{
			BidID: *input.BidID,
		})
		if !bidResp.Ok() {
			return utils.InvalidResponse("Bid not found", "BID_NOT_FOUND")
		}
		bidInfo := bidResp.Data[0]
		if bidInfo.Status != model.BidStatus.WIN {
			return utils.InvalidResponse("Only winning bid can create new contract", "INVALID_BID")
		}

		contractRes := model.ContractDB.QueryOne(&model.Contract{
			ContractNumber: input.ContractNumber,
			BidID:          &bidInfo.BidID,
		})

		if contractRes.Ok() {
			return utils.InvalidResponse("Contract number existed", "CONTRACT_NUMBER_EXISTED")
		}

		contractInfo.BidID = &bidInfo.BidID
		contractInfo.InvitationOfBid = &bidInfo.InvitationOfBid
		contractInfo.MainCategory = bidInfo.MainCategory

		queryLot := &model.Lot{
			BidID: bidInfo.BidID,
		}

		queryLot.ComplexQuery = append(queryLot.ComplexQuery, &primitive.M{
			"lot_id": bson.M{
				"$in": utils.Unique(lotIDs),
			},
		})

		lotsRes := model.LotDB.Query(queryLot, 0, int64(len(lotIDs)), nil)
		if !lotsRes.Ok() {
			return utils.InvalidResponse("Lot not found", "LOT_NOT_FOUND")
		}

		for _, item := range lotsRes.Data {
			mapLots[item.LotID] = item
		}
	}

	// validate beneficiary
	beneficiaryResp := model.BeneficiaryDB.QueryOne(&model.Beneficiary{
		TaxCode: input.TaxCode,
	})
	if !beneficiaryResp.Ok() {
		newBeneficiaryID := model.GenBeneficiaryID()
		newBeneficiary := &model.Beneficiary{
			Name:          input.BeneficiaryName,
			Address:       input.Address,
			PhoneNumber:   input.PhoneNumber,
			TaxCode:       input.TaxCode,
			Status:        model.BeneficiaryStatus.ACTIVE,
			CreatedByID:   contractInfo.CreatedByID,
			CreatedByName: contractInfo.CreatedByName,
			OrgID:         contractInfo.OrgID,
			EntityID:      contractInfo.EntityID,
			BeneficiaryID: newBeneficiaryID,
			Code:          model.ConvertToCode(newBeneficiaryID),
			Scope:         scope,
		}
		newBeneficiary.HashTag = model.GenBeneficiaryHashTag(newBeneficiary)

		beneficiaryResp = model.BeneficiaryDB.Create(newBeneficiary)

		if !beneficiaryResp.Ok() {
			return beneficiaryResp
		}

		beneficiary := beneficiaryResp.Data[0]
		contractInfo.BeneficiaryID = beneficiary.BeneficiaryID
		contractInfo.BeneficiaryCode = beneficiary.Code
	} else {
		beneficiary := beneficiaryResp.Data[0]
		contractInfo.BeneficiaryID = beneficiary.BeneficiaryID
		contractInfo.BeneficiaryCode = beneficiary.Code
	}

	if input.BidID != nil {
		contractRes := model.ContractDB.QueryOne(&model.Contract{
			ContractNumber: input.ContractNumber,
			BidID:          contractInfo.BidID,
		})
		if contractRes.Ok() {
			return utils.InvalidResponse(fmt.Sprintf("Contract number %s existed", input.ContractNumber), "CONTRACT_NUMBER_EXISTED")
		}
	} else {
		contractRes := model.ContractDB.QueryOne(&model.Contract{
			ContractNumber: input.ContractNumber,
			BeneficiaryID:  contractInfo.BeneficiaryID,
		})
		if contractRes.Ok() {
			return utils.InvalidResponse(fmt.Sprintf("Contract number %s existed", input.ContractNumber), "CONTRACT_NUMBER_EXISTED")
		}
	}

	contractInfo.HashTag = model.GenContractHashTag(contractInfo)

	if actionSource != nil {
		if actionSource.Account != nil {
			contractInfo.CreatedByID = actionSource.Account.AccountID
			contractInfo.CreatedByName = actionSource.Account.FullName
		}
		if actionSource.Session != nil {
			contractInfo.OrgID = actionSource.Session.OrgID
			contractInfo.EntityID = actionSource.Session.EntityID
		}
	}

	attachments := make([]string, 0)
	if len(contractInfo.Attachments) > 0 {
		integrationPayload := &integration.CreateContractDocumentRequest{
			OrgID:           contractInfo.OrgID,
			Name:            contractInfo.Name,
			Description:     contractInfo.Content,
			Tags:            []string{contractInfo.LegalEntityBranch},
			SearchText:      contractInfo.Name,
			LegalEntityCode: "BUYMED",
			SharedTarget: struct {
				SharedType string "json:\"sharedType\""
				Viewer     struct {
					Users       []int64                   "json:\"users\""
					Departments []*integration.Department "json:\"departments\""
				} "json:\"viewer\""
			}{
				SharedType: "DIRECT_ACCESS",
				Viewer: struct {
					Users       []int64                   "json:\"users\""
					Departments []*integration.Department "json:\"departments\""
				}{
					Users: []int64{contractInfo.CreatedByID},
					Departments: []*integration.Department{
						{
							Branch: "VN",
							Code:   "TDR",
						},
					},
				},
			},
			Documents: make([]struct {
				URL string "json:\"url\""
			}, 0),
			ExtendDocuments: make([]struct {
				Name string "json:\"name\""
				URL  string "json:\"url\""
			}, 0),
			CustomerInfo: struct {
				UserFullName    string "json:\"userFullName\""
				IdentityType    string "json:\"identityType\""
				IdentityNumber  string "json:\"identityNumber\""
				UserPhoneNumber string "json:\"userPhoneNumber\""
			}{
				UserFullName:    contractInfo.BeneficiaryName,
				IdentityType:    "MST",
				IdentityNumber:  contractInfo.TaxCode,
				UserPhoneNumber: contractInfo.PhoneNumber,
			},
			Refs: []map[string]string{
				{
					"key":   strings.ToLower(contractInfo.LegalEntityBranch),
					"value": contractInfo.ContractNumber,
				},
			},
		}
		for _, doc := range contractInfo.Attachments {
			integrationPayload.Documents = append(integrationPayload.Documents, struct {
				URL string "json:\"url\""
			}{
				URL: doc,
			})
			attachments = append(attachments, doc)
		}

		for _, doc := range contractInfo.Attachments {
			integrationPayload.ExtendDocuments = append(integrationPayload.ExtendDocuments, struct {
				Name string "json:\"name\""
				URL  string "json:\"url\""
			}{
				URL: doc,
			})
			attachments = append(attachments, doc)
		}

		traditionalContractCode, err := integration.CreateContractDocument(integrationPayload)
		if err != nil {
			return utils.InvalidResponse(err.Error(), "CREATE_CONTRACT_ERROR")
		}
		contractInfo.TraditionalContractCode = &traditionalContractCode
	}

	contractInfo.ContractID = model.GenContractID()
	contractInfo.Code = model.ConvertToCode(contractInfo.ContractID)
	contractInfo.Status = model.ContractStatus.DRAFT

	arrProductUnit := make([]*pim.ProductUnit, 0)
	for _, item := range input.Products {
		arrProductUnit = append(arrProductUnit, &pim.ProductUnit{
			ProductID:    item.ProductID,
			FromUnit:     item.Unit,
			FromQuantity: 1,
		})
	}
	arrProductUnit = utils.Unique(arrProductUnit)

	productUnits, err := pim.ExchangeUnit(arrProductUnit)
	if err != nil {
		return utils.InvalidResponse(err.Error(), "UNIT_NOT_FOUND")
	}

	mapProductUnit := make(map[int64]*pim.ProductUnitResponse)
	for _, item := range productUnits.Products {
		mapProductUnit[item.ProductID] = item
	}

	for _, item := range input.Products {
		if mapProductUnit[item.ProductID] == nil || len(mapProductUnit[item.ProductID].Rate) != 2 {
			return utils.InvalidResponse(fmt.Sprintf("Product %d not match unit", item.ProductID), "UNIT_NOT_FOUND")
		}
		rates := mapProductUnit[item.ProductID].Rate

		// set price for  1 quantity
		retailPriceTmp := item.Price * 1 * rates[1] / rates[0]

		job.JobCreateSkuToMarketplace.Push(&job.CreateSkuMsg{
			Sku: &product.SkuMainRequest{
				Code:        model.ConvertToCode(model.GenLotID()),
				SellerCode:  contractInfo.LegalEntityBranch,
				ProductCode: item.ProductCode,
				ProductID:   item.ProductID,
				Slug:        utils.NormalizeString(item.LotName), // random hash name
				Type:        "NORMAL",
				Status:      "NORMAL",
			},
			SkuItem: &product.SkuItemRequest{
				SKU:              fmt.Sprintf("%s.%s", contractInfo.LegalEntityBranch, item.ProductCode),
				SellerCode:       contractInfo.LegalEntityBranch,
				SellerClass:      "INTERNAL",
				ProductCode:      item.ProductCode,
				ProductID:        item.ProductID,
				Status:           "NORMAL",
				RetailPriceType:  "FIXED_PRICE",
				RetailPriceValue: retailPriceTmp + retailPriceTmp*10/100,
				PurchasePrice:    retailPriceTmp,
				VAT:              5,
				LocationCodes:    []string{"MIENTRUNG", "MIENBAC", "MIENNAM"},
			},
		}, &db_queue.ItemMetadata{
			Topic:     "create_sku_to_marketplace",
			ReadyTime: utils.AnyToPointer(time.Now()),
			UniqueKey: fmt.Sprintf("%s.%s", contractInfo.LegalEntityBranch, item.ProductCode),
			Keys: []string{
				fmt.Sprintf("%s.%s", contractInfo.LegalEntityBranch, item.ProductCode),
			},
		})
	}

	contractResp := model.ContractDB.Create(contractInfo)
	if !contractResp.Ok() {
		return contractResp
	}

	contract := contractResp.Data[0]

	for _, item := range input.Products {
		productItem := &model.Product{
			Code:           model.ConvertToCode(model.GenBidProductID()),
			ContractID:     contract.ContractID,
			ContractType:   "MAIN_CONTRACT",
			ContractCode:   contract.Code,
			MainContractID: contract.ContractID,
			OrgID:          contract.OrgID,
			EntityID:       contract.EntityID,
			CreatedByID:    contract.CreatedByID,
			CreatedByName:  contract.CreatedByName,
			Scope:          scope,
		}
		if input.BidID != nil {
			productItem.LotID = item.LotID
			if lotItem := mapLots[item.LotID]; lotItem != nil {
				productItem.GuaranteeAmount = lotItem.GuaranteeAmount
				productItem.Sku = item.Sku
				productItem.ProductID = item.ProductID
				productItem.ProductCode = item.ProductCode
				productItem.ProductName = item.LotName
				productItem.Quantity = item.Quantity
				productItem.QuantityInitial = item.Quantity
				productItem.QuantityRemain = item.Quantity
				productItem.Price = lotItem.LotPrice
				productItem.Amount = item.Quantity * lotItem.LotPrice
				productItem.GroupMedicine = lotItem.GroupMedicine
				productItem.Unit = lotItem.Unit
				productItem.RegistrationNo = lotItem.RegistrationNo
				productItem.Volume = lotItem.Volume
				productItem.VAT = lotItem.VAT
				productItem.ManufacturerName = lotItem.ManufacturerName
				productItem.OriginName = lotItem.OriginName
			}
		} else {
			productItem.Sku = item.Sku
			productItem.ProductID = item.ProductID
			productItem.ProductCode = item.ProductCode
			productItem.Quantity = item.Quantity
			productItem.Price = item.Price
			productItem.Amount = item.Quantity * item.Price
			productItem.Unit = item.Unit
		}

		products = append(products, productItem)
	}

	productsRes := model.ProductDB.CreateManyWithCtx(context.TODO(), products)
	if !productsRes.Ok() {
		return productsRes
	}

	if len(attachments) > 0 {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: attachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": contract.ContractNumber,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	return contractResp
}

// QueryContractList ...
func QueryContractList(query *request.ContractQuery, search string, offset, limit int64, queryOptions request.QueryOption, actionSource *request.ActionSource) common.Response {
	search = strings.TrimSpace(search)
	query.ContractNumber = strings.TrimSpace(query.ContractNumber)
	query.InvitationOfBid = strings.TrimSpace(query.InvitationOfBid)

	// Generate query scope based on action source
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	if search != "" {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", regexp.QuoteMeta(utils.ParserQ(search))), Options: ""},
		})
	}

	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": query.CreatedTo,
			},
		})
	}

	if len(query.ContractIDs) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"contract_id": bson.M{
				"$in": utils.Unique(query.ContractIDs),
			},
		})
	} else if len(query.ContractCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"code": bson.M{
				"$in": utils.Unique(query.ContractCodes),
			},
		})
	} else {
		query.ContractType = "DEBT"
	}

	resp := model.ContractDB.Query(query, offset, limit, &primitive.D{{Key: "_id", Value: -1}})
	if !resp.Ok() {
		return resp
	}

	if queryOptions.Total {
		resp.Total = model.ContractDB.Count(query).Total
	}
	return resp
}

// GetContractInfo ...
func GetContractInfo(contractID int64, contractCode string, options []string, actionSource *request.ActionSource) common.Response {
	if contractID <= 0 && contractCode == "" {
		return utils.InvalidResponse("Invalid ContractID", "INVALID_CONTRACT_ID")
	}

	query := &model.Contract{}
	if contractID > 0 {
		query.ContractID = contractID
	} else {
		query.Code = contractCode
	}

	// Generate query scope based on action source
	scope := utils.GenQueryScope(*actionSource)
	if scope != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	resp := model.ContractDB.QueryOne(query)
	if !resp.Ok() {
		return resp
	}

	contract := resp.Data[0]
	if utils.Contains(options, "contractBalance") {
		// cheat to pass
		// contract.Debt = &request.ContractAccountingBalanceResponse{
		// 	DocumentDataCode:      "-",
		// 	TotalBalanceTemporary: ***********,
		// }
		if contract.AccountingRefCode != nil && len(*contract.AccountingRefCode) > 0 {
			debt, err := integration.GetContractAccountingBalance(*contract.AccountingRefCode)
			if err == nil {
				contract.Debt = debt
			}
		}
	}

	return &common.APIResponse[*model.Contract]{
		Status: common.APIStatus.Ok,
		Data: []*model.Contract{
			contract,
		},
		Total: resp.Total,
	}
}

// UpdateContract ...
func UpdateContract(input *request.UpdateContractRequest, actionSource *request.ActionSource) common.Response {
	if input.Code == "" {
		return utils.InvalidResponse("Invalid Contract Code", "INVALID_CONTRACT_CODE")
	}

	// Check scope for the action source
	scope := utils.GenQueryScope(*actionSource)
	if scope == nil { // allow admin
	} else { // empty array => invalid
		input.ComplexQuery = append(input.ComplexQuery, &bson.M{
			"scope": bson.M{"$in": scope},
		})
	}

	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code: input.Code,
	})

	if !contractResp.Ok() {
		return utils.NotfoundDataResponse[any]("Not found any matched contract", "CONTRACT_NOT_FOUND")
	}

	contractInfo := contractResp.Data[0]

	contractScopeCheck := model.Contract{}

	if input.LegalEntityCode == nil || len(*input.LegalEntityCode) == 0 {
		contractScopeCheck.LegalEntityCode = contractInfo.LegalEntityCode
	} else {
		contractScopeCheck.LegalEntityCode = *input.LegalEntityCode
	}

	// permission check
	err := ValidateUpdateContractScope(*actionSource, contractScopeCheck)
	if err != nil {
		return &common.APIResponse[*model.Contract]{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.PermissionDenied),
		}
	}

	if contractInfo.Status == model.ContractStatus.DRAFT &&
		input.Status != nil &&
		*input.Status == string(model.ContractStatus.ACTIVE) {
		if contractInfo.SigningDate == nil {
			return utils.NotfoundDataResponse[any]("Signing date required", "SIGN_DATE_MISSING")
		}
		if contractInfo.ExpireDate == nil {
			return utils.NotfoundDataResponse[any]("Expire date required", "EXPIRE_DATE_MISSING")
		}
	}

	updater := &model.Contract{}

	if contractInfo.BidID != nil && *contractInfo.BidID > 0 {
		bidResp := model.BidDB.QueryOne(&model.Bid{
			BidID: *contractInfo.BidID,
		})
		if bidResp.Ok() && len(contractInfo.MainCategory) == 0 {
			updater.MainCategory = bidResp.Data[0].MainCategory
		}
	}

	if input.Status != nil && model.IsValidContractStatus(*input.Status) {
		updater.Status = model.ContractStatusValue(*input.Status)
	}

	if input.SigningDate != nil {
		updater.SigningDate = input.SigningDate
	}

	if input.ExpireDate != nil {
		updater.ExpireDate = input.ExpireDate
	}

	if input.Attachments != nil {
		updater.Attachments = *input.Attachments
	}

	if input.ExtendAttachments != nil {
		updater.ExtendAttachments = *input.ExtendAttachments
	}

	if input.IsStoreDocument != nil {
		updater.IsStoreDocument = input.IsStoreDocument
	}

	if input.DebtLimit != nil && *input.DebtLimit != updater.DebtLimit {
		updater.DebtLimit = *input.DebtLimit
	}

	if input.PaymentTerm != nil && *input.PaymentTerm != updater.PaymentTerm {
		updater.PaymentTerm = *input.PaymentTerm
	}

	if input.LegalEntityCode != nil && len(*input.LegalEntityCode) > 0 {
		updater.LegalEntityCode = *input.LegalEntityCode
	}

	if input.LegalEntityName != nil && len(*input.LegalEntityName) > 0 {
		updater.LegalEntityName = *input.LegalEntityName
	}

	if input.LegalEntityTaxCode != nil && len(*input.LegalEntityTaxCode) > 0 {
		updater.LegalEntityTaxCode = *input.LegalEntityTaxCode
	}

	if input.LegalEntityAddress != nil && len(*input.LegalEntityAddress) > 0 {
		updater.LegalEntityAddress = *input.LegalEntityAddress
	}

	if input.LegalEntityEmail != nil && len(*input.LegalEntityEmail) > 0 {
		updater.LegalEntityEmail = *input.LegalEntityEmail
	}

	if input.LegalEntityTel != nil && len(*input.LegalEntityTel) > 0 {
		updater.LegalEntityTel = *input.LegalEntityTel
	}

	if input.LegalEntityBank != nil {
		if len(input.LegalEntityBank.BankCode) > 0 {
			updater.LegalEntityBankCode = input.LegalEntityBank.BankCode
		}
		if len(input.LegalEntityBank.BankName) > 0 {
			updater.LegalEntityBankName = input.LegalEntityBank.BankName
		}
		if len(input.LegalEntityBank.BankBranch) > 0 {
			updater.LegalEntityBankBranch = input.LegalEntityBank.BankBranch
		}
		if len(input.LegalEntityBank.AccountNumber) > 0 {
			updater.LegalEntityBankAccount = input.LegalEntityBank.AccountNumber
		}
	}

	if input.ContractNumber != nil && len(*input.ContractNumber) > 0 {
		updater.ContractNumber = *input.ContractNumber
	}

	if input.Name != nil && len(*input.Name) > 0 && *input.Name != contractInfo.Name {
		updater.Name = strings.TrimSpace(*input.Name)
	}

	if input.PhoneNumber != nil && len(*input.PhoneNumber) > 0 {
		updater.PhoneNumber = strings.Replace(*input.PhoneNumber, " ", "", -1)
		updater.PhoneNumber = strings.Replace(updater.PhoneNumber, ".", "", -1)
	}

	if input.Email != nil && len(*input.Email) > 0 {
		updater.Email = *input.Email
	}

	if input.Address != nil && len(*input.Address) > 0 {
		updater.Address = *input.Address
	}

	if input.BeneficiaryName != nil && len(*input.BeneficiaryName) > 0 {
		updater.BeneficiaryName = *input.BeneficiaryName
	}
	if input.BudgetUnitCode != nil {
		updater.BudgetUnitCode = input.BudgetUnitCode
		if len(*updater.BudgetUnitCode) > 0 && len(*updater.BudgetUnitCode) != 7 {
			return utils.InvalidResponse("Mã ngân sách phải là 7 kí tự", "INVALID_BUDGET_UNIT_CODE")
		}
	}

	// updater.TaxCode = strings.Replace(contractInfo.TaxCode, " ", "", -1)
	updater.HashTag = model.GenContractHashTag(contractInfo)

	if input.TaxCode != "" && contractInfo.Status == model.ContractStatus.DRAFT {
		input.TaxCode = utils.NormalizeTaxCode(input.TaxCode)
		err := CheckTax(input.TaxCode)
		if err != nil {
			return utils.InvalidResponse(err.Error(), "INVALID_TAX_CODE")
		}
		updater.TaxCode = input.TaxCode
	}

	resp := model.ContractDB.UpdateOne(model.Contract{
		ContractID: contractInfo.ContractID,
	}, updater)

	if !resp.Ok() {
		return resp
	}
	contractAfterUpdated := resp.Data[0]
	if input.Attachments != nil && len(*input.Attachments) > 0 && utils.CompareArrays(contractInfo.Attachments, *input.Attachments) {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: *input.Attachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": contractInfo.ContractNumber,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	if input.ExtendAttachments != nil && len(*input.ExtendAttachments) > 0 && utils.CompareArrays(contractInfo.ExtendAttachments, *input.ExtendAttachments) {
		err := integration.MakeConfirmContractDocument(&integration.ConfirmContractDocument{
			Links: *input.ExtendAttachments,
			Refs: []map[string]string{
				{
					"key":   "tender",
					"value": contractInfo.ContractNumber,
				},
			},
		})
		if err != nil {
			fmt.Printf("error: %s", err.Error())
		}
	}

	// trigger make contract accounting
	if contractInfo.Status == model.ContractStatus.DRAFT &&
		input.Status != nil &&
		*input.Status == string(model.ContractStatus.ACTIVE) &&
		len(conf.Config.TemplateCode) > 0 &&
		contractInfo.DebtLimit > 0 {
		newMsg := &job.CreateContractAccountingRequest{
			Name:         contractInfo.Name,
			TemplateCode: conf.Config.TemplateCode,
			Status:       "ACTIVE",
			Data: job.DataContractAccountingRequest{
				ContractID:       contractInfo.ContractID,
				ContractNo:       contractInfo.ContractNumber,
				ContractFile:     contractInfo.Attachments,
				ContractAddendum: contractInfo.ExtendAttachments,
				PartyA:           contractInfo.LegalEntityName,
				PartyAType:       "COMPANY",
				PartyACode:       contractInfo.LegalEntityCode,
				TaxCodeA:         contractInfo.LegalEntityTaxCode,
				AddressA:         contractInfo.LegalEntityAddress,
				BankAccountA:     contractInfo.LegalEntityBankAccount,
				BankNameA:        contractInfo.LegalEntityBankName,
				EmailA:           contractInfo.LegalEntityEmail,
				PhoneNumberA:     contractInfo.LegalEntityTel,
				PartyB:           contractInfo.BeneficiaryName,
				PartyBType:       "TENDERVN_CUSTOMER",
				TaxCodeB:         contractAfterUpdated.TaxCode,
				AddressB:         contractInfo.Address,
				PhoneNumberB:     contractInfo.PhoneNumber,
				EmailB:           contractInfo.Email,
				DebtLimit:        contractInfo.DebtLimit,
				PaymentTerm:      contractInfo.PaymentTerm,
				StartDate:        contractInfo.SigningDate,
				EndDate:          contractInfo.ExpireDate,
				PartyBCode:       contractInfo.BeneficiaryCode,
			},
		}
		job.JobSyncContractToAccounting.Push(newMsg, &db_queue.ItemMetadata{
			Topic:     "default",
			ReadyTime: utils.AnyToPointer(time.Now().Add(30 * time.Second)),
			UniqueKey: fmt.Sprint(contractInfo.ContractID),
			SortedKey: fmt.Sprint(contractInfo.ContractID),
			Keys: []string{
				fmt.Sprint(contractInfo.ContractID),
			},
		})
	}

	return resp
}

func MigrateContractDebt(contractCode string) common.Response {
	contractRs := model.ContractDB.QueryOne(&model.Contract{
		Code: contractCode,
	})
	if !contractRs.Ok() {
		return contractRs
	}
	contractInfo := contractRs.Data[0]
	if contractInfo.AccountingRefCode != nil {
		return &common.APIResponse[any]{
			ErrorCode: "DEBT_EXISTED",
			Status:    common.APIStatus.Error,
			Message:   "Contract had config debt before",
		}
	}

	if contractInfo.Status != model.ContractStatus.ACTIVE {
		return &common.APIResponse[any]{
			ErrorCode: "CONTRACT_INVALID",
			Status:    common.APIStatus.Error,
			Message:   "Invalid status",
		}
	}

	if len(contractInfo.LegalEntityName) == 0 {
		return &common.APIResponse[any]{
			ErrorCode: "LEGAL_INFO_INVALID",
			Status:    common.APIStatus.Error,
			Message:   "Invalid LegalEntityName",
		}
	}

	if len(contractInfo.LegalEntityTaxCode) == 0 {
		return &common.APIResponse[any]{
			ErrorCode: "LEGAL_INFO_INVALID",
			Status:    common.APIStatus.Error,
			Message:   "Invalid LegalEntityTaxCode",
		}
	}

	if len(contractInfo.LegalEntityBankAccount) == 0 {
		return &common.APIResponse[any]{
			ErrorCode: "LEGAL_INFO_INVALID",
			Status:    common.APIStatus.Error,
			Message:   "Invalid LegalEntityBankAccount",
		}
	}

	if contractInfo.SigningDate == nil {
		return &common.APIResponse[any]{
			ErrorCode: "CONTRACT_INFO_INVALID",
			Status:    common.APIStatus.Error,
			Message:   "Invalid SigningDate",
		}
	}

	if contractInfo.ExpireDate == nil {
		return &common.APIResponse[any]{
			ErrorCode: "CONTRACT_INFO_INVALID",
			Status:    common.APIStatus.Error,
			Message:   "Invalid ExpireDate",
		}
	}

	newMsg := &job.CreateContractAccountingRequest{
		Name:         contractInfo.Name,
		TemplateCode: conf.Config.TemplateCode,
		Status:       "ACTIVE",
		Data: job.DataContractAccountingRequest{
			ContractID:       contractInfo.ContractID,
			ContractNo:       contractInfo.ContractNumber,
			ContractFile:     contractInfo.Attachments,
			ContractAddendum: contractInfo.ExtendAttachments,
			PartyA:           contractInfo.LegalEntityName,
			PartyAType:       "COMPANY",
			PartyACode:       contractInfo.LegalEntityCode,
			TaxCodeA:         contractInfo.LegalEntityTaxCode,
			AddressA:         contractInfo.LegalEntityAddress,
			BankAccountA:     contractInfo.LegalEntityBankAccount,
			BankNameA:        contractInfo.LegalEntityBankName,
			EmailA:           contractInfo.LegalEntityEmail,
			PhoneNumberA:     contractInfo.LegalEntityTel,
			PartyB:           contractInfo.BeneficiaryName,
			PartyBType:       "TENDERVN_CUSTOMER",
			TaxCodeB:         contractInfo.TaxCode,
			AddressB:         contractInfo.Address,
			PhoneNumberB:     contractInfo.PhoneNumber,
			EmailB:           contractInfo.Email,
			DebtLimit:        contractInfo.DebtLimit,
			PaymentTerm:      contractInfo.PaymentTerm,
			StartDate:        contractInfo.SigningDate,
			EndDate:          contractInfo.ExpireDate,
			PartyBCode:       contractInfo.BeneficiaryCode,
		},
	}

	job.JobSyncContractToAccounting.Push(newMsg, &db_queue.ItemMetadata{
		Topic:     "default",
		ReadyTime: utils.AnyToPointer(time.Now().Add(30 * time.Second)),
		UniqueKey: fmt.Sprint(contractInfo.ContractID),
		SortedKey: fmt.Sprint(contractInfo.ContractID),
		Keys: []string{
			fmt.Sprint(contractInfo.ContractID),
		},
	})

	return contractRs
}

func MigrateContractQuantity(contractCode string) common.Response {
	contractRs := model.ContractDB.QueryOne(&model.Contract{
		Code: contractCode,
	})
	if !contractRs.Ok() {
		return contractRs
	}

	contractInfo := contractRs.Data[0]
	productRs := model.ProductDB.Query(&model.Product{
		ContractID:   contractInfo.ContractID,
		ContractType: "MAIN_CONTRACT",
	}, 0, 1000, nil)
	if productRs.Ok() {
		products := productRs.Data
		for _, item := range products {
			if item.Quantity > 0 && (item.QuantitySold == nil || *item.QuantitySold == 0) {
				updater := &model.Product{}
				if item.QuantityInitial == 0 {
					updater.QuantityInitial = item.Quantity
				}

				if item.QuantityRemain == 0 {
					updater.QuantityRemain = item.Quantity
				}

				_ = model.ProductDB.UpdateOne(&model.Product{
					Code: item.Code,
				}, updater)
			}

			if len(item.ProductName) == 0 {
				lotRs := model.LotDB.QueryOne(&model.Lot{
					LotID: item.LotID,
					BidID: item.BidID,
				})
				if lotRs.Ok() {
					lotInfo := lotRs.Data[0]
					_ = model.ProductDB.UpdateOne(&model.Product{
						Code: item.Code,
					}, &model.Product{
						ProductName: lotInfo.LotName,
					})
				}
			}

			if len(item.ProductCode) == 0 {
				lotRs := model.LotDB.QueryOne(&model.Lot{
					LotID: item.LotID,
					BidID: item.BidID,
				})
				if lotRs.Ok() {
					lotInfo := lotRs.Data[0]
					_ = model.ProductDB.UpdateOne(&model.Product{
						Code: item.Code,
					}, &model.Product{
						ProductCode: lotInfo.ProductCode,
					})
				}
			}

			if item.VAT == nil || *item.VAT <= 0 {
				_ = model.ProductDB.UpdateOne(&model.Product{
					Code: item.Code,
				}, &model.Product{
					VAT: utils.AnyToPointer(5),
				})
			}
		}
	}
	return contractRs
}

// func validate create contract
func ValidateCreateContractScope(actionSource request.ActionSource, contractRequest *request.CreateContractRequest) error {
	if contractRequest.LegalEntityCode == "" {
		return errors.New("contract legal entity is nil")
	}
	return utils.ValidateContractScope(actionSource, contractRequest.LegalEntityCode)
}

// func validate update contract
func ValidateUpdateContractScope(actionSource request.ActionSource, contractRequest model.Contract) error {
	if contractRequest.LegalEntityCode == "" {
		return errors.New("contract legal entity is nil")
	}
	return utils.ValidateContractScope(actionSource, contractRequest.LegalEntityCode)
}
