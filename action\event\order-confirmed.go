package event

import (
	"errors"
	"fmt"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/db_queue"
)

// OnOrderConfirmed ...
func OnOrderConfirmed(order *model.Order) error {

	// get contract info
	contractResp := model.ContractDB.QueryOne(&model.Contract{
		Code:   order.ContractCode,
		Status: model.ContractStatus.ACTIVE,
	})

	if !contractResp.Ok() {
		return errors.New("error= order contract not found")
	}

	orderContractInfo := contractResp.Data[0]
	channelOrder := fmt.Sprintf("%s_VN", orderContractInfo.LegalEntityBranch)

	orderTags := []string{channelOrder}

	// force do not export invoice for TAYAU
	if orderContractInfo.LegalEntityBranch == "TAYAU" {
		orderTags = []string{}
		order.CanExportInvoice = utils.AnyToPointer(false)

		if order.Invoice != nil && order.Invoice.InvoiceRequest != nil {
			order.Invoice.InvoiceRequest = utils.AnyToPointer(false)
		}
	}

	newMsg := &job.OrderOms{
		OrderID:                 order.OrderID,
		InternalOrderID:         order.OrderID,
		OrderCode:               order.OrderCode,
		InternalOrderCode:       order.OrderCode,
		Channel:                 channelOrder,
		Currency:                order.Currency,
		AccountID:               order.CreatedByID,
		CreatedByAccountId:      order.CreatedByID,
		CreatedByName:           order.CreatedByName,
		CustomerType:            orderContractInfo.LegalEntityBranch,
		Status:                  order.Status,
		CustomerID:              order.ReceiverID,
		CustomerCode:            order.ReceiverCode,
		CustomerName:            order.ReceiverName,
		CustomerPhone:           order.ReceiverPhoneNumber,
		CustomerShippingAddress: order.ReceiverShippingAddress,
		CustomerProvinceCode:    order.ProvinceCode,
		CustomerDistrictCode:    order.DistrictCode,
		CustomerWardCode:        order.WardCode,
		Price:                   order.TotalAmount,
		TotalPrice:              order.TotalAmount,
		PaymentMethod:           order.PaymentMethod,
		DeliveryMethod:          order.DeliveryMethod,
		ConfirmationDate:        order.LastUpdatedTime,
		Source:                  order.Source,
		Note:                    &order.Note,
		OrgID:                   order.OrgID,
		EntityID:                order.EntityID,
		Tags:                    orderTags,
		Invoice:                 order.Invoice,
		CreatedTime:             order.CreatedTime,
		AccountingRefCode:       order.AccountingRefCode,
		CanExportInvoice: func(fl *bool) bool {
			if fl == nil || !*fl {
				return false
			}
			return true
		}(order.CanExportInvoice),
		Scope: orderContractInfo.Scope,
	}

	orderItem := make([]*job.OrderItem, 0)
	orderItemResult := model.OrderItemDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
		OrgID:   order.OrgID,
	}, 0, 1000, nil)

	if !orderItemResult.Ok() {
		return errors.New("error= order items not found")
	}

	for _, item := range orderItemResult.Data {
		orderItem = append(orderItem, &job.OrderItem{
			InternalOrderID:   item.OrderID,
			InternalOrderCode: item.OrderCode,
			Sku:               fmt.Sprintf("%s.%s", orderContractInfo.LegalEntityBranch, item.ProductCode),
			IsImportant:       utils.AnyToPointer(true),
			Type:              item.Type,
			SellerCode:        item.SellerCode,
			SellerClass:       item.SellerClass,
			ProductID:         item.ProductID,
			ProductCode:       item.ProductCode,
			ProductName:       item.ProductName,
			SkuStatus:         item.SkuStatus,
			SkuPriceType:      item.SkuPriceType,
			VAT:               item.VAT,
			Price:             int(item.Price),
			TotalPrice:        int(item.TotalAmount),
			Quantity:          int(item.Quantity),
			Unit:              item.Unit,
			UnitName:          item.UnitName,
			ConfigUnit:        item.ConfigUnit,
			Volume:            item.Volume,
		})
	}

	newMsg.OrderItems = orderItem

	job.JobSyncOrderToOMS.Push(newMsg, &db_queue.ItemMetadata{
		Topic:     "default",
		ReadyTime: utils.AnyToPointer(time.Now().Add(30 * time.Second)),
		UniqueKey: fmt.Sprint(order.OrderID),
		SortedKey: fmt.Sprint(order.OrderID),
		Keys: []string{
			fmt.Sprint(order.OrderID),
		},
	})

	return nil
}
