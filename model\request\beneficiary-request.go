package request

import "go.mongodb.org/mongo-driver/bson"

type BeneficiaryQuery struct {
	BeneficiaryID    int64     `json:"beneficiaryID,omitempty" bson:"beneficiary_id,omitempty"`
	BeneficiaryCode  int64     `json:"beneficiaryCode,omitempty" bson:"beneficiary_code,omitempty"`
	BeneficiaryIDs   []int64   `json:"beneficiaryIDs,omitempty" bson:"-"`
	BeneficiaryCodes []string  `json:"beneficiaryCodes,omitempty" bson:"-"`
	Status           string    `json:"status,omitempty" bson:"status,omitempty"`
	ComplexQuery     []*bson.M `json:"-" bson:"$and,omitempty"`
}
