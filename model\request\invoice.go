package request

import "time"

type BuyerTaxGOV struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	BuyerTaxCode string `json:"buyerTaxCode,omitempty" bson:"buyer_tax_code,omitempty"` // bắt buộc nếu là KH doanh nghiệp
	BuyerName    string `json:"buyerName,omitempty" bson:"buyer_name,omitempty"`
	BuyerAddress string `json:"buyerAddress,omitempty" bson:"buyer_address,omitempty"`

	BuyerTaxGOVStatus      string `json:"buyerTaxGOVStatus,omitempty" bson:"buyer_tax_gov_status,omitempty"`           // Trạng thái MST của CQT
	BuyerTaxGOVDescription string `json:"buyerTaxGOVDescription,omitempty" bson:"buyer_tax_gov_description,omitempty"` // Mô tả Trạng thái MST của CQT

	IsSync bool `json:"isSyncHilo,omitempty" bson:"-"`
}


