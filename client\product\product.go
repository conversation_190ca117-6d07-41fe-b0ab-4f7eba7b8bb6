package product

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout            = 30 * time.Second
	pathSearchProductFuzzy    = "/marketplace/product/v2/search-component/fuzzy"
	pathGetProductByProductID = "/marketplace/product/v2/product/list"
)

type client struct {
	host          string
	defaultClient *sdk_client.RestClient
	headers       map[string]string
}

type searchProductFuzzyRequest struct {
	Type   string `json:"type"`
	Text   string `json:"text,omitempty"`
	Limit  int64  `json:"limit"`
	Offset int64  `json:"offset"`
	Filter struct {
		Sellers []string `json:"sellers"`
	} `json:"filter"`
}

// ProductFuzzy ...
type ProductFuzzy struct {
	Name      string `json:"name"`
	Unit      string `json:"unit"`
	Volume    string `json:"volume"`
	ProductID int64  `json:"productID"`
	Slug      string `json:"slug"`
	Code      string `json:"code"`
}

// SkuItem ...
type SkuItem struct {
	Sku           string   `json:"sku"`
	ItemCode      string   `json:"itemCode"`
	IsActive      bool     `json:"isActive"`
	LocationCodes []string `json:"locationCodes"`
	ProductID     int64    `json:"productID"`
	ProductCode   string   `json:"productCode"`
	Status        string   `json:"status"`
}

type ProductResponse struct {
	Name          string   `json:"name"`
	Unit          string   `json:"unit"`
	Volume        string   `json:"volume"`
	ProductID     int64    `json:"productID"`
	ProductCode   string   `json:"productCode"`
	ProductSlug   string   `json:"productSlug"`
	Sku           string   `json:"sku,omitempty"`
	ItemCode      string   `json:"itemCode,omitempty"`
	IsActive      bool     `json:"isActive,omitempty"`
	LocationCodes []string `json:"locationCodes,omitempty"`
	Status        string   `json:"status,omitempty"`
}

type ProductDataFuzzy struct {
	Product *ProductFuzzy `json:"product"`
	SkuItem *SkuItem      `json:"skuItem"`
}

type SearchSkuFuzzyResponse struct {
	Data  []*ProductResponse `json:"data"`
	Total int64              `json:"total"`
}

// ProductFuzzyResponse ...
type ProductFuzzyResponse struct {
	Data  []*ProductDataFuzzy `json:"data"`
	Total int64               `json:"total"`
}

// GetByIDSRequest ...
type getByIDSRequest struct {
	Ids   []int64  `json:"ids"`
	Codes []string `json:"codes"`
}

type GetProductByProductIDResponse struct {
	Data  []*ProductFuzzy `json:"data"`
	Total int64           `json:"total"`
}

var productClient *client

// InitProductClient ...
func InitProductClient(db *mongo.Database) {
	productClient = &client{
		host: conf.Config.BuymedVNClient.Host,
		headers: map[string]string{
			"Authorization": conf.Config.BuymedVNClient.Authorization,
		},
		defaultClient: sdk_client.NewRESTClient(conf.Config.BuymedVNClient.Host, "product_client", defaultTimeout, 0, 0),
	}
	productClient.defaultClient.SetDBLog(db)
}

// SearchProductFuzzy ...
func SearchProductFuzzy(search, seller string, productIDs []int64) (*SearchSkuFuzzyResponse, error) {
	if len(productIDs) > 0 {
		return GetProductByProductID(productIDs)
	}

	payload := &searchProductFuzzyRequest{
		Type:   "PRODUCT",
		Text:   search,
		Offset: 0,
		Limit:  30,
	}

	if len(seller) > 0 {
		payload = &searchProductFuzzyRequest{
			Type:   "SKU",
			Text:   search,
			Offset: 0,
			Limit:  30,
			Filter: struct {
				Sellers []string "json:\"sellers\""
			}{
				Sellers: []string{seller},
			},
		}
	}

	fmt.Println(payload)

	resp, err := productClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, productClient.headers, nil, payload, pathSearchProductFuzzy)
	if err != nil {
		return nil, err
	}

	if resp.Code != http.StatusOK {
		return nil, errors.New("error: fail to call api search fuzzy")
	}

	fmt.Println(resp.Body)

	var data *ProductFuzzyResponse
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return nil, errors.New("error: fail to read data")
	}

	products := make([]*ProductResponse, 0)
	for _, item := range data.Data {
		if item.Product == nil {
			continue
		}

		newItem := &ProductResponse{
			Name:        item.Product.Name,
			Unit:        item.Product.Unit,
			ProductSlug: item.Product.Slug,
			Volume:      item.Product.Volume,
			ProductID:   item.Product.ProductID,
			ProductCode: item.Product.Code,
		}

		if item.SkuItem != nil {
			newItem.ProductID = item.SkuItem.ProductID
			newItem.ProductCode = item.SkuItem.ProductCode
			newItem.Sku = item.SkuItem.Sku
			newItem.ItemCode = item.SkuItem.ItemCode
			newItem.LocationCodes = item.SkuItem.LocationCodes
			newItem.IsActive = item.SkuItem.IsActive
			newItem.Status = item.SkuItem.Status
		}
		products = append(products, newItem)

	}
	if len(products) == 0 {
		return nil, errors.New("error: search not match")
	}

	return &SearchSkuFuzzyResponse{
		Total: data.Total,
		Data:  products,
	}, nil
}

// GetProductByProductID ...
func GetProductByProductID(productIDs []int64) (*SearchSkuFuzzyResponse, error) {
	payload := &getByIDSRequest{
		Ids: productIDs,
	}

	resp, err := productClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, productClient.headers, nil, payload, pathGetProductByProductID)
	if err != nil {
		return nil, err
	}

	if resp.Code != http.StatusOK {
		return nil, errors.New("error: fail to call api search fuzzy")
	}

	var data *GetProductByProductIDResponse
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return nil, errors.New("error: fail to read data")
	}

	products := make([]*ProductResponse, 0)
	for _, item := range data.Data {

		products = append(products, &ProductResponse{
			ProductID:   item.ProductID,
			ProductCode: item.Code,
			Name:        item.Name,
			Unit:        item.Unit,
			ProductSlug: item.Slug,
			Volume:      item.Volume,
		})
	}
	if len(products) == 0 {
		return nil, errors.New("error: search not match")
	}

	return &SearchSkuFuzzyResponse{
		Total: data.Total,
		Data:  products,
	}, nil
}
