package request

import (
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"go.mongodb.org/mongo-driver/bson"
)

// LotQuery ...
type LotQuery struct {
	LotIDs       []int64   `json:"lotIDs,omitempty" bson:"-"`
	BidID        int64     `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	Status       string    `json:"status,omitempty" bson:"status,omitempty"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// UpdateLotRequest ...
type UpdateLotRequest struct {
	LotLineID        *int64  `json:"lotLineID,omitempty" bson:"lot_line_id,omitempty"`
	LotID            int64   `json:"lotID,omitempty" bson:"lot_id,omitempty"`
	LotName          *string `json:"lotName,omitempty" bson:"lot_name,omitempty"`
	LotPrice         *int64  `json:"lotPrice,omitempty" bson:"lot_price,omitempty"`
	Quantity         *int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	Unit             *string `json:"unit,omitempty" bson:"unit,omitempty"`
	ProductID        *int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode      *string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	RegistrationNo   *string `json:"registrationNo,omitempty" bson:"registration_no,omitempty"`
	GuaranteeAmount  *int64  `json:"guaranteeAmount,omitempty" bson:"guarantee_amount,omitempty"`
	Status           *string `json:"status,omitempty" bson:"status,omitempty"`
	Note             *string `json:"note,omitempty" bson:"note,omitempty"`
	Volume           *string `json:"volume,omitempty" bson:"volume,omitempty"`
	ManufacturerName *string `json:"manufacturerName,omitempty" bson:"manufacturer_name,omitempty"`
	OriginName       *string `json:"originName,omitempty" bson:"origin_name,omitempty"`
	VAT              *int    `json:"vat,omitempty" bson:"vat,omitempty"`
}

// CreateLotRequest ...
type CreateLotRequest struct {
	LotLineID        int64  `json:"lotLineID,omitempty" bson:"lot_line_id,omitempty"`
	BidID            int64  `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	ProductID        int64  `json:"productID,omitempty"`
	ProductCode      string `json:"productCode,omitempty"`
	LotName          string `json:"lotName,omitempty" bson:"lot_name,omitempty"`
	LotPrice         int64  `json:"lotPrice,omitempty" bson:"lot_price,omitempty"`
	LotPriceUnit     string `json:"lotPriceUnit,omitempty" bson:"lot_price_unit,omitempty"`
	LotAmount        int64  `json:"lotAmount,omitempty" bson:"lot_amount,omitempty"`
	Quantity         int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	GuaranteeAmount  int64  `json:"guaranteeAmount,omitempty" bson:"guarantee_amount,omitempty"`
	GroupMedicine    int64  `json:"groupMedicine,omitempty" bson:"group_medicine,omitempty"`
	Unit             string `json:"unit,omitempty" bson:"unit,omitempty"`
	RegistrationNo   string `json:"registrationNo,omitempty" bson:"registration_no,omitempty"`
	Volume           string `json:"volume,omitempty" bson:"volume,omitempty"`
	ManufacturerName string `json:"manufacturerName,omitempty" bson:"manufacturer_name,omitempty"`
	OriginName       string `json:"originName,omitempty" bson:"origin_name,omitempty"`
	VAT              *int   `json:"vat,omitempty" bson:"vat,omitempty"`
}

// Validate ...
func (m *CreateLotRequest) Validate() *common.APIResponse[any] {
	if m.LotLineID <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Line ID",
			ErrorCode: "INVALID_LINE_ID",
		}
	}
	if m.BidID <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid BidID",
			ErrorCode: "INVALID_BID_ID",
		}
	}

	// if m.ProductID <= 0 {
	// 	return &common.APIResponse[any]{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Invalid ProductID",
	// 		ErrorCode: "INVALID_PRODUCT_ID",
	// 	}
	// }

	// if len(m.ProductCode) == 0 {
	// 	return &common.APIResponse[any]{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Invalid ProductCode",
	// 		ErrorCode: "INVALID_PRODUCT_CODE",
	// 	}
	// }

	if len(m.LotName) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Lot name is required",
			ErrorCode: "INVALID_LOT_NAME",
		}
	}

	if m.LotPrice <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Lot price must be greater than zero",
			ErrorCode: "INVALID_LOT_PRICE",
		}
	}

	if m.Quantity <= 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Quantity must be greater than zero",
			ErrorCode: "INVALID_QUANTITY",
		}
	}

	if m.GuaranteeAmount < 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Guarantee amount must be greater than one",
			ErrorCode: "INVALID_GUARANTEE_AMOUNT",
		}
	}

	if m.ProductID > 0 && (m.GroupMedicine <= 0 || m.GroupMedicine > 9) {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Group medicine must be between 1 and 9",
			ErrorCode: "INVALID_GROUP_MEDICINE",
		}
	}

	if m.VAT != nil && *m.VAT < 0 || *m.VAT > 100 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "VAT invalid",
			ErrorCode: "INVALID_VAT",
		}
	}

	if len(m.Unit) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Unit is required",
			ErrorCode: "INVALID_UNIT",
		}
	}

	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}
