// Package model ...
package model

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// LotStatusValue ...
type LotStatusValue string

// LotStatusEnt ...
type LotStatusEnt struct {
	WIN     LotStatusValue
	FAIL    LotStatusValue
	WAITING LotStatusValue
}

// LotStatus ...
var LotStatus = &LotStatusEnt{
	"WIN",
	"FAIL",
	"WAITING",
}

// Lot ...
type Lot struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string         `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64          `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string         `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64          `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64          `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64          `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	Status        LotStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	LotLineID       int64  `json:"lotLineID,omitempty" bson:"lot_line_id,omitempty"`
	LotID           int64  `json:"lotID,omitempty" bson:"lot_id,omitempty"`
	ProductID       int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode     string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	BidID           int64  `json:"bidID,omitempty" bson:"bid_id,omitempty"`
	LotName         string `json:"lotName,omitempty" bson:"lot_name,omitempty"`
	LotPrice        int64  `json:"lotPrice,omitempty" bson:"lot_price,omitempty"`
	LotPriceUnit    string `json:"lotPriceUnit,omitempty" bson:"lot_price_unit,omitempty"` // VND
	LotAmount       int64  `json:"lotAmount,omitempty" bson:"lot_amount,omitempty"`
	Quantity        int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	GuaranteeAmount int64  `json:"guaranteeAmount,omitempty" bson:"guarantee_amount,omitempty"`
	GroupMedicine   int64  `json:"groupMedicine,omitempty" bson:"group_medicine,omitempty"`
	Unit            string `json:"unit,omitempty" bson:"unit,omitempty"`
	RegistrationNo  string `json:"registrationNo,omitempty" bson:"registration_no,omitempty"`
	VAT             *int   `json:"vat,omitempty" bson:"vat,omitempty"`
	Note            string `json:"note,omitempty" bson:"note,omitempty"`

	ManufacturerName string `json:"manufacturerName,omitempty" bson:"manufacturer_name,omitempty"`
	OriginName       string `json:"originName,omitempty" bson:"origin_name,omitempty"`
	Volume           string `json:"volume,omitempty" bson:"volume,omitempty"`

	HashTag string `json:"-" bson:"hash_tag,omitempty"` // for search

	// ForQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// LotDB ...
var LotDB = &mongodb.Instance[*Lot]{
	ColName: "lots",
}

// InitLotModel ....
func InitLotModel(s *mongo.Database) {
	LotDB.ApplyDatabase(s)

	t := true
	LotDB.CreateIndex(bson.D{
		{Key: "lot_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	LotDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	LotDB.CreateIndex(bson.D{
		{Key: "status", Value: 1},
	}, &options.IndexOptions{})

	LotDB.CreateIndex(bson.D{
		{Key: "lot_name", Value: 1},
		{Key: "bid_id", Value: 1},
	}, &options.IndexOptions{})

	err := LotDB.CreateIndex(bson.D{
		{Key: "bid_id", Value: 1},
		{Key: "lot_line_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})
	if err != nil {
		fmt.Println(err.Error())
	}
}

// GenLotHashTag ...
func GenLotHashTag(lot *Lot) string {
	return strings.Replace(utils.NormalizeString(lot.LotName), " ", "-", -1)
}

// IsValidLotStatus ...
func IsValidLotStatus(status string) bool {
	switch LotStatusValue(status) {
	case LotStatus.WIN, LotStatus.FAIL, LotStatus.WAITING:
		{
			return true
		}
	}
	return false
}
