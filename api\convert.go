package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// ReviewProductUnit ...
func ReviewProductUnit(req server.APIRequest, resp server.APIResponder) error {
	var input request.ReviewProductUnitRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}
	return resp.Respond(action.ReviewProductUnit(&input))
}

// QueryProductUnit ...
func QueryProductUnit(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.ProductUnitQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	return resp.Respond(action.QueryProductUnit(&input.Q, input.Search, input.Offset, input.Limit, input.Option))
}
