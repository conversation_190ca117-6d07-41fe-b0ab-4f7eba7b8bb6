// Package main ...
package main

import (
	"fmt"
	"os"
	"runtime"
	"strconv"
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action/tool"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/api"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/integration"
	invoice_v2 "gitlab.buymed.tech/buymed.com/tender/core-tender/client/invoice-v2"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/oms"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/order"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/product"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/conf"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/job"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	namespace   = "tender"
	serviceName = "core-tender"
)

type infoData struct {
	Service     string    `json:"service"`
	Environment string    `json:"environment"`
	Version     string    `json:"version"`
	StartTime   time.Time `json:"startTime"`
}

var globalInfo *infoData

func info(req server.APIRequest, res server.APIResponder) error {
	return res.Respond(&common.APIResponse[*infoData]{
		Status:  common.APIStatus.Ok,
		Data:    []*infoData{globalInfo},
		Message: fmt.Sprint(runtime.NumGoroutine()),
	})
}

func main() {
	// Load config
	conf.LoadAppConfig(namespace, serviceName)

	globalInfo = &infoData{
		Service:     conf.Config.AppName,
		Version:     os.Getenv("version"),
		Environment: conf.Config.Env,
		StartTime:   time.Now(),
	}

	// New app
	_app := server.NewApplication(conf.Config.AppName)

	// Init DB
	initDBAllDB(_app)

	// setup API Server
	protocol := os.Getenv("protocol")
	if protocol == "" {
		protocol = "THRIFT"
	}
	_server, _ := _app.SetupAPIServer(protocol)
	_ = _server.SetHandler(common.APIMethod.GET, "/api-info", info)

	{
		_ = _server.SetHandler(common.APIMethod.POST, "/bid", api.CreateBid)
		_ = _server.SetHandler(common.APIMethod.PUT, "/bid/info", api.UpdateBid)
		_ = _server.SetHandler(common.APIMethod.GET, "/bid/info", api.GetBidInfo)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/bid/list", api.QueryBidList)
	}

	// lots of bid
	{
		_ = _server.SetHandler(common.APIMethod.POST, "/lot", api.CreateLot)
		_ = _server.SetHandler(common.APIMethod.PUT, "/lot", api.UpdateLot)
		_ = _server.SetHandler(common.APIMethod.GET, "/lot", api.GetLotInfo)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/lot/list", api.QueryLotList)

	}

	// contract
	{
		_ = _server.SetHandler(common.APIMethod.POST, "/contract", api.CreateContract)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/contract/list", api.QueryContractList)
		_ = _server.SetHandler(common.APIMethod.GET, "/contract", api.GetContractInfo)
		_ = _server.SetHandler(common.APIMethod.PUT, "/contract", api.UpdateContract)

		_ = _server.SetHandler(common.APIMethod.QUERY, "/contract-annex/list", api.QueryContractAnnexList)
		_ = _server.SetHandler(common.APIMethod.POST, "/contract-annex", api.CreateContractAnnex)
		_ = _server.SetHandler(common.APIMethod.GET, "/contract-annex", api.GetContractAnnexInfo)
		_ = _server.SetHandler(common.APIMethod.PUT, "/contract-annex", api.UpdateContractAnnex)
	}

	// product
	{
		_ = _server.SetHandler(common.APIMethod.POST, "/product", api.CreateProduct)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/product/list", api.QueryProductList)
		_ = _server.SetHandler(common.APIMethod.GET, "/product", api.GetProductInfo)
		_ = _server.SetHandler(common.APIMethod.PUT, "/product", api.UpdateProduct)
		_ = _server.SetHandler(common.APIMethod.POST, "/product-component/fuzzy", api.SearchProductFuzzyWithProxy)
		_ = _server.SetHandler(common.APIMethod.POST, "/product/unit-convert", api.ReviewProductUnit)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/product/unit-convert", api.QueryProductUnit)
	}

	// order
	{
		_ = _server.SetHandler(common.APIMethod.QUERY, "/order/proxy", api.QueryOrderList)
		_ = _server.SetHandler(common.APIMethod.POST, "/order", api.CreateOrder)
		_ = _server.SetHandler(common.APIMethod.DELETE, "/order", api.RemoveOrderDraft)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/order/list", api.QueryOrderBidList)
		_ = _server.SetHandler(common.APIMethod.PUT, "/order/status", api.UpdateOrderStatus)
		_ = _server.SetHandler(common.APIMethod.GET, "/order", api.GetOrderInfo)

		_ = _server.SetHandler(common.APIMethod.PUT, "/sale-order", api.UpdateSaleOrderFulfillment)
		_ = _server.SetHandler(common.APIMethod.PUT, "/cancel-order", api.CancelOrder)
		_ = _server.SetHandler(common.APIMethod.GET, "/migrate-order-cancel", api.MigrateCancelOrder)

		// _ = _server.SetHandler(common.APIMethod.GET, "/invoice-exchange", api.RequestExportInvoiceExchange)
		//
	}

	// beneficiary
	{
		_ = _server.SetHandler(common.APIMethod.POST, "/beneficiary", api.CreateBeneficiary)
		_ = _server.SetHandler(common.APIMethod.QUERY, "/beneficiary/list", api.QueryBeneficiaryList)
	}

	// migrate
	_ = _server.SetHandler(common.APIMethod.PUT, "/contract/debt", api.MigrateContractDebt)
	_ = _server.SetHandler(common.APIMethod.PUT, "/contract/quantity", api.MigrateContractQuantity)
	_ = _server.SetHandler(common.APIMethod.PUT, "/tool/migrate-legal-entity-branch", api.MigrateContractLegalEntityBranch)

	// tool
	{
		_ = _server.SetHandler(common.APIMethod.POST, "/tool/update-contract-taxcode", api.ToolUpdateContractTaxCode)
		_ = _server.SetHandler(common.APIMethod.POST, "/tool/update-order-taxcode", api.ToolUpdateOrderTaxCode)
		_ = _server.SetHandler(common.APIMethod.POST, "/tool/sync-order-invoice", api.ToolSyncOrderInvoice)
		_ = _server.SetHandler(common.APIMethod.PUT, "/tool/force-cancel-order", api.ToolForceCancelOrder)
		_ = _server.SetHandler(common.APIMethod.PUT, "/tool/migrate-scope", api.MigrateScope)

		// tool support
		_ = _server.SetHandler(common.APIMethod.PUT, "/tool/contract-product", tool.UpdateProductContract)
		_ = _server.SetHandler(common.APIMethod.PUT, "/tool/lot", tool.UpdateLot)

	}

	// expose
	port := 80
	portConfig := os.Getenv("port")
	if portConfig != "" {
		port, _ = strconv.Atoi(portConfig)
	}
	_server.Expose(port)

	// worker
	_app.SetAllDBConnected(func() {
		// _ = action.Migrate()

		// callback
		job.JobCreateSkuToMarketplace.SetTopicConsumer("create_sku_to_marketplace", action.HandleCreateSku)
		job.JobCreateSkuToMarketplace.StartConsume()

		job.JobSyncOrderToOMS.SetTopicConsumer("default", action.HandleSyncOrderToOms)
		job.JobSyncOrderToOMS.StartConsume()

		job.JobSyncContractToAccounting.SetTopicConsumer("default", action.HandleSyncContractToAccounting)
		job.JobSyncContractToAccounting.StartConsume()
	})

	// launch app
	_app.Launch()
}

func initDBAllDB(_app *server.Application) {

	// setup main database
	_app.SetupDBClient(mongodb.Configuration{
		Address:     conf.Config.MainDBConf.Addr,
		Username:    conf.Config.MainDBConf.User,
		Password:    conf.Config.MainDBConf.Password,
		AuthDB:      conf.Config.MainDBConf.Auth,
		DoWriteTest: true,
		DBName:      conf.Config.MainDBConf.DatabaseName,
	}, onDBConnected)

	_app.SetupDBClient(mongodb.Configuration{
		Address:     conf.Config.JobDBConf.Addr,
		Username:    conf.Config.JobDBConf.User,
		Password:    conf.Config.JobDBConf.Password,
		AuthDB:      conf.Config.JobDBConf.Auth,
		DoWriteTest: true,
		DBName:      conf.Config.JobDBConf.DatabaseName,
	}, onDBJobConnected)

	_app.SetupDBClient(mongodb.Configuration{
		Address:     conf.Config.LogDBConf.Addr,
		Username:    conf.Config.LogDBConf.User,
		Password:    conf.Config.LogDBConf.Password,
		AuthDB:      conf.Config.LogDBConf.Auth,
		DoWriteTest: true,
		DBName:      conf.Config.LogDBConf.DatabaseName,
	}, onDBLogConnected)
}

// onDBConnected function that handle on connected to DB event
func onDBConnected(s *mongo.Database) error {
	fmt.Println("Connected to DB")

	model.InitIdGenModel(s)
	model.InitBidModel(s)
	model.InitLotModel(s)
	model.InitContractModel(s)
	model.InitProductModel(s)
	model.InitContractAnnexModel(s)
	model.InitOrderModel(s)
	model.InitOrderItemModel(s)
	model.InitBeneficiaryModel(s)

	return nil
}

// onDBLogConnected function that handle on connected to DB event
func onDBLogConnected(s *mongo.Database) error {
	fmt.Println("Connected to LogDB")

	// client
	product.InitProductClient(s)
	integration.InitContractClient(s)
	order.InitOrderProxyClient(s)
	oms.InitProxyOmsClient(s)
	pim.InitPimClient(s)

	invoice_v2.InitInvoiceV2client(s)

	return nil
}

// onDBJobConnected function that handle on connected to DB event
func onDBJobConnected(s *mongo.Database) error {

	fmt.Println("Connected to JobDB")

	job.InitJobCreateSkuToMarketplace(s)
	job.InitJobSyncOrderToOMS(s)
	job.InitJobSyncContractToAccounting(s)

	return nil
}
