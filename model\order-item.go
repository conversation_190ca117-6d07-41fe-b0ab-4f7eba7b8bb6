package model

import (
	"time"

	"gitlab.buymed.tech/buymed.com/tender/core-tender/client/pim"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/sdk/go-sdk-v2/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Order Item ...
type OrderItem struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`
	UpdatedByName string `json:"updatedByName,omitempty" bson:"updated_by_name,omitempty"`
	UpdatedByID   int64  `json:"updatedByID,omitempty" bson:"updated_by_id,omitempty"`
	OrgID         int64  `json:"orgID,omitempty" bson:"org_id,omitempty" `
	EntityID      int64  `json:"entityID,omitempty" bson:"entity_id,omitempty"`
	Status        string `json:"status,omitempty" bson:"status,omitempty"`

	OrderID   int64  `json:"orderID,omitempty" bson:"order_id,omitempty"`
	OrderCode string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	ItemID    int64  `json:"itemID,omitempty" bson:"item_id,omitempty"`
	ItemCode  string `json:"itemCode,omitempty" bson:"item_code,omitempty"`

	Sku                 string   `json:"sku,omitempty" bson:"sku,omitempty"`
	IsImportant         *bool    `json:"isImportant,omitempty" bson:"is_important,omitempty"`
	Type                string   `json:"type,omitempty" bson:"type,omitempty"`
	SellerCode          string   `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerClass         string   `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	Volume              string   `json:"volume,omitempty" bson:"volume,omitempty"`
	ManufacturerName    string   `json:"manufacturerName,omitempty" bson:"manufacturer_name,omitempty"`
	OriginName          string   `json:"originName,omitempty" bson:"origin_name,omitempty"`
	ProductName         string   `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode         string   `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID           int64    `json:"productID,omitempty" bson:"product_id,omitempty"`
	LotID               int64    `json:"lotID,omitempty" bson:"lot_id,omitempty"`
	ContractProductCode string   `json:"contractProductCode,omitempty" bson:"contract_product_code,omitempty"`
	SkuStatus           string   `json:"skuStatus,omitempty" bson:"sku_status,omitempty"`
	SkuPriceType        string   `json:"skuPriceType,omitempty" bson:"sku_price_type,omitempty"`
	SkuVersion          string   `json:"skuVersion,omitempty" bson:"sku_version,omitempty"`
	SkuLevel            string   `json:"skuLevel,omitempty" bson:"sku_level,omitempty"`
	Tags                []string `json:"tags,omitempty" bson:"tags,omitempty"`
	VAT                 *int     `json:"vat,omitempty" bson:"vat,omitempty"`

	Price             int64                       `json:"price,omitempty" bson:"price,omitempty,omitempty"`
	ActualPrice       *int64                      `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	TotalAmount       int64                       `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	Quantity          int64                       `json:"quantity,omitempty" bson:"quantity,omitempty"` // order quantity
	CompletedQuantity *int64                      `json:"completedQuantity,omitempty" bson:"completed_quantity,omitempty"`
	ReturnedQuantity  *int64                      `json:"returnedQuantity,omitempty" bson:"returned_quantity,omitempty"`
	Unit              string                      `json:"unit,omitempty" bson:"unit,omitempty"` // order unit
	UnitName          string                      `json:"unitName,omitempty" bson:"unit_name,omitempty"`
	SkuUnit           string                      `json:"skuUnit,omitempty" bson:"sku_unit,omitempty"`         // standard unit
	SkuQuantity       int64                       `json:"skuQuantity,omitempty" bson:"sku_quantity,omitempty"` // convert from <order unit> and <order quantity>
	ConfigUnit        *pim.ProductUnitResponse    `json:"configUnit,omitempty" bson:"config_unit,omitempty"`
	CompletedInfos    []*request.ItemQuantityInfo `json:"completedInfos,omitempty" bson:"completed_infos,omitempty"`
	ReturnInfos       []*request.ItemQuantityInfo `json:"returnInfos,omitempty" bson:"return_infos,omitempty"`
}

// OrderItemDB ...
var OrderItemDB = &mongodb.Instance[*OrderItem]{
	ColName: "order_items",
}

// InitOrderItemModel ....
func InitOrderItemModel(s *mongo.Database) {
	OrderItemDB.ApplyDatabase(s)

	t := true
	OrderItemDB.CreateIndex(bson.D{
		{Key: "item_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	OrderItemDB.CreateIndex(bson.D{
		{Key: "item_code", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	// new
	OrderItemDB.CreateIndex(bson.D{
		{Key: "order_id", Value: 1},
	}, &options.IndexOptions{})

	OrderItemDB.CreateIndex(bson.D{
		{Key: "order_code", Value: 1},
	}, &options.IndexOptions{})
}
