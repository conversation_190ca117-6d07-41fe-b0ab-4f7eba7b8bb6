package product

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	sdk_client "gitlab.buymed.tech/sdk/go-sdk-v2/client"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
)

const (
	pathCreateSkuMain     = "/marketplace/product/v2/sku-main"
	pathCreateSkuItem     = "/marketplace/product/v2/sku-item"
	pathCheckSkuItemExist = "/marketplace/product/v2/sku-item/list"
)

func CreateSkuMain(payload *SkuMainRequest) error {
	resp, err := productClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, productClient.headers, nil, payload, pathCreateSkuMain)
	if err != nil {
		fmt.Println(err.Error())
		return err
	}

	if resp.Code != http.StatusOK {
		return errors.New("error: fail to call api create sku main")
	}

	var data *CreateSkuResponse
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return errors.New("error: fail to read data resp")
	}

	if data.Status != common.APIStatus.Ok && data.Status != common.APIStatus.Existed {
		return errors.New("error: fail to create sku " + strings.ToLower(data.Message))
	}
	return nil
}

func CreateSkuItem(payload *SkuItemRequest) error {
	err := CheckSkuItemExist(payload.SKU, payload.SellerCode)
	if err == nil {
		return errors.New("error: sku existed")
	}

	resp, err := productClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Post, productClient.headers, nil, payload, pathCreateSkuItem)
	if err != nil {
		fmt.Println(err.Error())
		return err
	}

	fmt.Printf("%v\n", resp)

	if resp.Code != http.StatusOK {
		return errors.New("error: fail to call api create sku item")
	}
	return nil
}

func CheckSkuItemExist(sku, sellerCode string) error {
	params := map[string]string{
		"q": fmt.Sprintf(`{"sku": "%s", "sellerCode": "%s"}`, sku, sellerCode),
	}

	resp, err := productClient.defaultClient.MakeHTTPRequest(sdk_client.HTTPMethods.Get, productClient.headers, params, nil, pathCheckSkuItemExist)
	if err != nil {
		fmt.Println(err.Error())
		return err
	}
	if resp.Code != http.StatusOK {
		return errors.New("error: fail to call find sku item")
	}

	var data *baseResponse[*Sku]
	err = json.Unmarshal([]byte(resp.Body), &data)
	if err != nil {
		return errors.New("error: fail to read data")
	}

	if len(data.Data) == 0 {
		return errors.New("error: sku not found")
	}

	return nil
}
