package tool

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

func UpdateLot(req server.APIRequest, resp server.APIResponder) error {
	var updater model.Lot
	if err := req.GetContent(&updater); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if updater.LotID == 0 {
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "updater.LotID == 0",
		})
	}

	oldResp := model.LotDB.QueryOne(&model.Lot{
		LotID: updater.LotID,
	})
	if !oldResp.Ok() {
		return resp.Respond(oldResp)
	}
	if updater.Quantity == 0 {
		updater.Quantity = oldResp.Data[0].Quantity
	}

	if updater.LotPrice != 0 {
		updater.LotAmount = updater.LotPrice * updater.Quantity
	}

	return resp.Respond(model.LotDB.UpdateOne(&model.Lot{
		LotID: updater.LotID,
	}, &updater))
}
