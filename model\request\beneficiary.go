package request

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
)

type CreateBeneficiaryRequest struct {
	Name        string                       `json:"name,omitempty" bson:"name,omitempty"`
	Address     string                       `json:"address,omitempty" bson:"address,omitempty"`
	PhoneNumber string                       `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
	TaxCode     string                       `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	Email       string                       `json:"email,omitempty" bson:"email,omitempty"`
	Status      model.BeneficiaryStatusValue `json:"status,omitempty" bson:"status,omitempty"`
}

func (m *CreateBeneficiaryRequest) Validate() *common.APIResponse[any] {
	if len(m.Name) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Name",
			ErrorCode: "INVALID_NAME",
		}
	}
	if len(m.Address) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Address",
			ErrorCode: "INVALID_ADDRESS",
		}
	}
	if len(m.TaxCode) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Tax",
			ErrorCode: "INVALID_TAX",
		}
	}
	if len(m.PhoneNumber) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Phone number",
			ErrorCode: "INVALID_PHONE_NUMBER",
		}
	}
	if len(m.Status) == 0 {
		return &common.APIResponse[any]{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Status",
			ErrorCode: "INVALID_Status",
		}
	}
	return &common.APIResponse[any]{
		Status: common.APIStatus.Ok,
	}
}
