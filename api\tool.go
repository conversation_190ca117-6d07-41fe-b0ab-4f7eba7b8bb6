package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// Tool update the contract tax code
func ToolUpdateContractTaxCode(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateContractTaxCodeRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.ToolUpdateContractTaxCode(&input, actionSource))
}

// Tool update the contract tax code
func ToolUpdateOrderTaxCode(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateOrderTaxCodeRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.ToolUpdateOrderTaxCode(&input, actionSource))
}

// Tool update the contract tax code
func ToolSyncOrderInvoice(req server.APIRequest, resp server.APIResponder) error {
	var input request.SyncOrderInvoiceRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.ToolSyncOrderInvoice(&input, actionSource))
}

// Tool cancel order without order status.
func ToolForceCancelOrder(req server.APIRequest, resp server.APIResponder) error {
	var input request.ForceCancelOrderRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.ToolForceCancelOrder(&input, actionSource))
}
