package api

import (
	"gitlab.buymed.tech/buymed.com/tender/core-tender/action"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/model/request"
	"gitlab.buymed.tech/buymed.com/tender/core-tender/utils"
	"gitlab.buymed.tech/sdk/go-sdk-v2/common"
	"gitlab.buymed.tech/sdk/go-sdk-v2/server"
)

// QueryContractAnnexList ...
func QueryContractAnnexList(req server.APIRequest, resp server.APIResponder) error {
	var input request.QueryInput[request.ContractAnnexQuery, request.QueryOption]

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	return resp.Respond(action.QueryContractAnnexList(&input.Q, input.Search, input.Offset, input.Limit, input.Option))
}

// CreateContractAnnex ...
func CreateContractAnnex(req server.APIRequest, resp server.APIResponder) error {
	var input request.CreateContractAnnexRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.CreateContractAnnex(&input, actionSource))
}

// GetContractAnnexInfo ...
func GetContractAnnexInfo(req server.APIRequest, resp server.APIResponder) error {
	params := req.GetParams()

	var (
		contractCode = params["contractCode"]
		code         = params["code"]
	)
	return resp.Respond(action.GetContractAnnexInfo(contractCode, code))
}

// UpdateContractAnnex ...
func UpdateContractAnnex(req server.APIRequest, resp server.APIResponder) error {
	var input request.UpdateContractAnnexRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(utils.InvalidParseJSONData(err))
	}

	actionSource := utils.GetActionSource(req)
	if actionSource == nil {
		// Respond with a permission denied error if action source is not found
		return resp.Respond(&common.APIResponse[any]{
			Status:  common.APIStatus.Invalid,
			Message: "Permission denied",
		})
	}

	return resp.Respond(action.UpdateContractAnnex(&input, actionSource))
}
